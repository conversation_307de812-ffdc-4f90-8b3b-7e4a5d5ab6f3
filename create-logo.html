<!DOCTYPE html>
<html>
<head>
    <title>Motimora Logo Creator</title>
    <style>
        body {
            display: flex;
            flex-direction: column;
            align-items: center;
            font-family: Arial, sans-serif;
        }
        canvas {
            border: 1px solid #ccc;
            margin: 10px;
        }
        .buttons {
            margin: 20px;
        }
        button {
            padding: 10px 20px;
            margin: 0 10px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <h1>Motimora Logo Creator</h1>
    
    <div>
        <canvas id="iconCanvas" width="1024" height="1024"></canvas>
        <p>icon.png (1024x1024)</p>
    </div>
    
    <div>
        <canvas id="adaptiveIconCanvas" width="1024" height="1024"></canvas>
        <p>adaptive-icon.png (1024x1024)</p>
    </div>
    
    <div>
        <canvas id="faviconCanvas" width="196" height="196"></canvas>
        <p>favicon.png (196x196)</p>
    </div>
    
    <div class="buttons">
        <button onclick="downloadIcon()">Download icon.png</button>
        <button onclick="downloadAdaptiveIcon()">Download adaptive-icon.png</button>
        <button onclick="downloadFavicon()">Download favicon.png</button>
    </div>
    
    <script>
        // Draw icon
        function drawIcon(canvas, size, bgColor, textColor) {
            const ctx = canvas.getContext('2d');
            
            // Background
            ctx.fillStyle = bgColor;
            ctx.fillRect(0, 0, size, size);
            
            // Text
            ctx.fillStyle = textColor;
            ctx.font = `bold ${size * 0.6}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('M', size / 2, size / 2);
        }
        
        // Initialize canvases
        const iconCanvas = document.getElementById('iconCanvas');
        const adaptiveIconCanvas = document.getElementById('adaptiveIconCanvas');
        const faviconCanvas = document.getElementById('faviconCanvas');
        
        // Draw logos
        drawIcon(iconCanvas, 1024, '#4CAF50', 'white'); // Green background with white M
        drawIcon(adaptiveIconCanvas, 1024, '#4CAF50', 'white'); // Green background with white M
        drawIcon(faviconCanvas, 196, '#4CAF50', 'white'); // Green background with white M
        
        // Download functions
        function downloadCanvas(canvas, filename) {
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL('image/png');
            link.click();
        }
        
        function downloadIcon() {
            downloadCanvas(iconCanvas, 'icon.png');
        }
        
        function downloadAdaptiveIcon() {
            downloadCanvas(adaptiveIconCanvas, 'adaptive-icon.png');
        }
        
        function downloadFavicon() {
            downloadCanvas(faviconCanvas, 'favicon.png');
        }
    </script>
</body>
</html>
