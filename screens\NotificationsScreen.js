import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  FlatList,
  StyleSheet,
  TouchableOpacity,
  Image,
  ActivityIndicator,
  RefreshControl,
  ToastAndroid
} from 'react-native';
import {
  collection,
  query,
  where,
  orderBy,
  onSnapshot,
  doc,
  getDoc,
  getDocs,
  updateDoc,
  deleteDoc,
  writeBatch,
  limit
} from 'firebase/firestore';
import { db, auth } from '../firebase';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';

// Avatar mapping
const avatarMap = {
  avatar1: require('../assets/avatar1.png'),
  avatar2: require('../assets/avatar2.png'),
  avatar3: require('../assets/avatar3.png'),
  avatar4: require('../assets/avatar4.png'),
  avatar5: require('../assets/avatar5.png'),
  avatar6: require('../assets/avatar6.png'),
  avatar7: require('../assets/avatar7.png'),
  avatar8: require('../assets/avatar8.png'),
  avatar9: require('../assets/avatar9.png'),
  avatar10: require('../assets/avatar10.png'),
  avatar11: require('../assets/avatar11.png'),
  avatar12: require('../assets/avatar12.png'),
  avatar13: require('../assets/avatar13.png'),
  avatar14: require('../assets/avatar14.png'),
};

// Bildirim türleri
const NOTIFICATION_TYPES = {
  LIKE: 'like',
  COMMENT: 'comment',
  FOLLOW: 'follow',
  MENTION: 'mention',
  REPLY: 'reply'
};

// Zaman formatı
function timeAgo(timestamp) {
  if (!timestamp || !timestamp.toDate) return 'Bilinmeyen Tarih';

  const now = new Date();
  const postTime = timestamp.toDate();
  const diff = Math.floor((now - postTime) / 1000);

  if (diff < 60) return `${diff} saniye önce`;
  if (diff < 3600) return `${Math.floor(diff / 60)} dakika önce`;
  if (diff < 86400) return `${Math.floor(diff / 3600)} saat önce`;
  if (diff < 2592000) return `${Math.floor(diff / 86400)} gün önce`;
  if (diff < 31536000) return `${Math.floor(diff / 2592000)} ay önce`;
  return `${Math.floor(diff / 31536000)} yıl önce`;
}

export default function NotificationsScreen() {
  const [notifications, setNotifications] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [userProfiles, setUserProfiles] = useState({});

  const navigation = useNavigation();
  const currentUser = auth.currentUser;

  // Bildirimleri yükle
  useEffect(() => {
    if (!currentUser) return;

    loadNotifications();

    // Bildirimleri okundu olarak işaretle
    markAllAsRead();
  }, [currentUser]);

  // Bildirimleri yükle
  const loadNotifications = () => {
    if (!currentUser) return;

    const notificationsRef = collection(db, 'notifications');
    // Not: Bu sorgu için Firestore'da bir indeks oluşturmanız gerekebilir
    // Eğer hata alırsanız, hata mesajındaki URL'yi kullanarak indeksi oluşturun
    // Alternatif olarak, aşağıdaki sorguyu kullanabilirsiniz (indeks gerektirmez):
    const q = query(
      notificationsRef,
      where('recipientId', '==', currentUser.uid)
      // orderBy('createdAt', 'desc'), - indeks gerektirir
      // limit(50)
    );

    const unsubscribe = onSnapshot(q, async (snapshot) => {
      const notificationsList = [];
      const userIds = new Set();

      snapshot.docs.forEach(doc => {
        const data = doc.data();
        userIds.add(data.senderId);
        notificationsList.push({
          id: doc.id,
          ...data
        });
      });

      // JavaScript tarafında sıralama yap (Firestore indeksi olmadan)
      notificationsList.sort((a, b) => {
        // createdAt null olabilir (yeni eklenen bildirimler için)
        if (!a.createdAt) return 1;
        if (!b.createdAt) return -1;

        // serverTimestamp() kullanıldığında toDate() metodu var
        const dateA = a.createdAt.toDate ? a.createdAt.toDate() : new Date(a.createdAt);
        const dateB = b.createdAt.toDate ? b.createdAt.toDate() : new Date(b.createdAt);

        return dateB - dateA; // Yeniden eskiye sıralama
      });

      // Kullanıcı profillerini yükle
      const profiles = { ...userProfiles };
      const promises = Array.from(userIds).map(async (userId) => {
        if (!profiles[userId]) {
          const userDoc = await getDoc(doc(db, 'users', userId));
          if (userDoc.exists()) {
            profiles[userId] = userDoc.data();
          }
        }
      });

      await Promise.all(promises);
      setUserProfiles(profiles);
      setNotifications(notificationsList);
      setLoading(false);
    });

    return unsubscribe;
  };

  // Bildirimleri okundu olarak işaretle
  const markAllAsRead = async () => {
    if (!currentUser) return;

    try {
      const notificationsRef = collection(db, 'notifications');
      const q = query(
        notificationsRef,
        where('recipientId', '==', currentUser.uid),
        where('read', '==', false)
      );

      const snapshot = await getDocs(q);

      if (snapshot.empty) return;

      const batch = writeBatch(db);
      snapshot.docs.forEach(doc => {
        batch.update(doc.ref, { read: true });
      });

      await batch.commit();
    } catch (error) {
      console.error('Bildirimleri okundu olarak işaretleme hatası:', error);
    }
  };

  // Yenileme işlemi
  const onRefresh = () => {
    setRefreshing(true);
    loadNotifications();
    setTimeout(() => setRefreshing(false), 1000);
  };

  // Bildirime tıklama
  const handleNotificationPress = (notification) => {
    if (!notification) return;

    // Bildirim türüne göre yönlendirme
    switch (notification.type) {
      case NOTIFICATION_TYPES.LIKE:
        navigation.navigate('Comments', { postId: notification.postId });
        break;
      case NOTIFICATION_TYPES.COMMENT:
      case NOTIFICATION_TYPES.REPLY:
        navigation.navigate('Comments', { postId: notification.postId });
        break;
      case NOTIFICATION_TYPES.FOLLOW:
        navigation.navigate('OtherProfile', { uid: notification.senderId });
        break;
      case NOTIFICATION_TYPES.MENTION:
        navigation.navigate('Comments', { postId: notification.postId });
        break;
      default:
        break;
    }
  };

  // Bildirimi silme
  const handleDeleteNotification = async (notificationId) => {
    try {
      await deleteDoc(doc(db, 'notifications', notificationId));
      setNotifications(prev => prev.filter(n => n.id !== notificationId));
      ToastAndroid.show('Bildirim silindi', ToastAndroid.SHORT);
    } catch (error) {
      console.error('Bildirim silme hatası:', error);
      ToastAndroid.show('Bildirim silinirken bir hata oluştu', ToastAndroid.SHORT);
    }
  };

  // Bildirim öğesi render
  const renderNotificationItem = ({ item }) => {
    const sender = userProfiles[item.senderId] || {};
    const profilePic = sender.profilePic || 'avatar1';

    // Bildirim içeriği
    let content = '';
    let icon = null;

    switch (item.type) {
      case NOTIFICATION_TYPES.LIKE:
        content = 'gönderinizi beğendi';
        icon = <Ionicons name="heart" size={16} color="#e74c3c" />;
        break;
      case NOTIFICATION_TYPES.COMMENT:
        content = 'gönderinize yorum yaptı';
        icon = <Ionicons name="chatbubble" size={16} color="#3498db" />;
        break;
      case NOTIFICATION_TYPES.FOLLOW:
        content = 'sizi takip etmeye başladı';
        icon = <Ionicons name="person-add" size={16} color="#2ecc71" />;
        break;
      case NOTIFICATION_TYPES.MENTION:
        content = 'sizi bir yorumda etiketledi';
        icon = <Ionicons name="at" size={16} color="#f39c12" />;
        break;
      case NOTIFICATION_TYPES.REPLY:
        content = 'yorumunuza cevap verdi';
        icon = <Ionicons name="return-down-forward" size={16} color="#9b59b6" />;
        break;
      default:
        content = 'size bildirim gönderdi';
        icon = <Ionicons name="notifications" size={16} color="#95a5a6" />;
    }

    return (
      <TouchableOpacity
        style={[
          styles.notificationItem,
          !item.read && styles.unreadNotification
        ]}
        onPress={() => handleNotificationPress(item)}
        onLongPress={() => handleDeleteNotification(item.id)}
      >
        <View style={styles.notificationContent}>
          <TouchableOpacity
            onPress={() => navigation.navigate('OtherProfile', { uid: item.senderId })}
          >
            <Image
              source={
                avatarMap[profilePic] || require('../assets/default-avatar.png')
              }
              style={styles.avatar}
            />
          </TouchableOpacity>

          <View style={styles.textContainer}>
            <Text style={styles.notificationText}>
              <Text style={styles.username}>{sender.username || 'Kullanıcı'}</Text>
              {' '}{content}
            </Text>
            <View style={styles.notificationMeta}>
              {icon}
              <Text style={styles.timestamp}>{timeAgo(item.createdAt)}</Text>
            </View>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      {/* Üst Bar */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Bildirimler</Text>
        <View style={styles.rightSpace} />
      </View>

      {loading ? (
        <ActivityIndicator size="large" color="#8e44ad" style={styles.loader} />
      ) : notifications.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Ionicons name="notifications-off-outline" size={64} color="#555" />
          <Text style={styles.emptyText}>Henüz bildiriminiz yok</Text>
        </View>
      ) : (
        <FlatList
          data={notifications}
          renderItem={renderNotificationItem}
          keyExtractor={item => item.id}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              tintColor="#fff"
            />
          }
          contentContainerStyle={styles.listContainer}
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
    paddingTop: 30
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 15,
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#333'
  },
  backButton: {
    padding: 5
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff'
  },
  rightSpace: {
    width: 24
  },
  loader: {
    marginTop: 20
  },
  listContainer: {
    paddingBottom: 20
  },
  notificationItem: {
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#222'
  },
  unreadNotification: {
    backgroundColor: 'rgba(142, 68, 173, 0.1)'
  },
  notificationContent: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 10,
    borderWidth: 1,
    borderColor: '#0066CC'
  },
  textContainer: {
    flex: 1
  },
  notificationText: {
    color: '#fff',
    fontSize: 14,
    marginBottom: 5
  },
  username: {
    fontWeight: 'bold',
    color: '#3498db'
  },
  notificationMeta: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  timestamp: {
    color: '#777',
    fontSize: 12,
    marginLeft: 5
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20
  },
  emptyText: {
    color: '#777',
    fontSize: 16,
    marginTop: 10,
    textAlign: 'center'
  }
});
