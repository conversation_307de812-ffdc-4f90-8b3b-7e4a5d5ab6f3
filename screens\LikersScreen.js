import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  FlatList,
  StyleSheet,
  ActivityIndicator,
  Image,
  TouchableOpacity,
  RefreshControl,
} from 'react-native';
import { doc, getDoc } from 'firebase/firestore';
import { db, auth } from '../firebase';
import { Ionicons } from '@expo/vector-icons';
import { useRoute, useNavigation } from '@react-navigation/native';

function LikersScreen() {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  const route = useRoute();
  const navigation = useNavigation();
  // route.params.likedBy: [ 'uid1', 'uid2', ... ]
  const { likedBy } = route.params || {};

  useEffect(() => {
    if (!likedBy || !Array.isArray(likedBy)) {
      setUsers([]);
      setLoading(false);
      return;
    }
    loadLikers();
  }, [likedBy]);

  async function loadLikers() {
    setLoading(true);
    try {
      const userPromises = likedBy.map(async (uid) => {
        const userDocRef = doc(db, 'users', uid);
        const snap = await getDoc(userDocRef);
        if (snap.exists()) {
          return { id: uid, ...snap.data() };
        }
        return null;
      });
      const results = (await Promise.all(userPromises)).filter(u => u !== null);
      setUsers(results);
    } catch (error) {
      console.log('LikersScreen error:', error);
    }
    setLoading(false);
  }

  function onRefresh() {
    setRefreshing(true);
    loadLikers().then(() => setRefreshing(false));
  }

  function renderItem({ item }) {
    return (
      <TouchableOpacity
        style={styles.userRow}
        onPress={() => navigation.navigate('OtherProfile', { uid: item.id })}
      >
        <Image
          source={
            item.photoURL
              ? { uri: item.photoURL }
              : require('../assets/default-avatar.png')
          }
          style={styles.avatar}
        />
        <Text style={styles.username}>{item.username || 'Kullanıcı'}</Text>
      </TouchableOpacity>
    );
  }

  if (loading) {
    return (
      <View style={styles.loaderContainer}>
        <ActivityIndicator size="large" color="#8e44ad" />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Üst Bar */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Beğenenler</Text>
        <View style={styles.rightSpace} />
      </View>

      {users.length === 0 ? (
        <Text style={styles.noLikesText}>Henüz beğenen yok.</Text>
      ) : (
        <FlatList
          data={users}
          keyExtractor={(item) => item.id}
          renderItem={renderItem}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} tintColor="#fff" />
          }
          contentContainerStyle={{ paddingBottom: 20 }}
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    height: 50,
    borderBottomWidth: 0.5,
    borderBottomColor: '#333',
    paddingHorizontal: 10,
    backgroundColor: '#000',
  },
  backButton: {
    padding: 5,
  },
  headerTitle: {
    flex: 1,
    textAlign: 'center',
    color: '#fff',
    fontSize: 20,
    fontWeight: 'bold',
  },
  rightSpace: {
    width: 40,
  },
  loaderContainer: {
    flex: 1,
    backgroundColor: '#000',
    justifyContent: 'center',
    alignItems: 'center',
  },
  noLikesText: {
    color: '#aaa',
    fontSize: 15,
    textAlign: 'center',
    marginTop: 30,
  },
  userRow: {
    flexDirection: 'row',
    alignItems: 'center',
    borderBottomColor: '#333',
    borderBottomWidth: 1,
    padding: 10,
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 8,
    borderWidth: 1,
    borderColor: '#666',
  },
  username: {
    fontSize: 15,
    color: '#fff',
  },
});

export default LikersScreen;
