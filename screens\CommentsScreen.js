import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  FlatList,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Image,
  Alert,
  Keyboard,
  RefreshControl,
  Animated,
  LayoutAnimation,
  PanResponder,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  ActivityIndicator,
  SafeAreaView,
  TouchableWithoutFeedback
} from 'react-native';

import {
  collection,
  query,
  orderBy,
  addDoc,
  updateDoc,
  onSnapshot,
  serverTimestamp,
  doc,
  getDoc,
  deleteDoc,
  increment
} from 'firebase/firestore';

import { Ionicons } from '@expo/vector-icons';
import { useRoute, useNavigation } from '@react-navigation/native';
import { auth, db } from '../firebase';
import { createNotification, NOTIFICATION_TYPES } from '../utils/notificationUtils';
import { updateUserPopularity, listenToPostComments } from '../utils/popularityUtils';

const COMMENT_CHAR_LIMIT = 200;
const MENTION_SUGGEST_LIMIT = 6;

/** "x dakika önce" şeklinde zaman dönüştürme */
function timeAgo(timestamp, timeNow) {
  if (!timestamp) return 'Bilinmeyen Tarih';
  const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
  if (isNaN(date.getTime())) return 'Bilinmeyen Tarih';
  const diffSec = Math.floor((timeNow - date.getTime()) / 1000);
  if (diffSec < 0) return '0 saniye önce';
  if (diffSec < 60) return diffSec + ' saniye önce';
  if (diffSec < 3600) return Math.floor(diffSec / 60) + ' dakika önce';
  if (diffSec < 86400) return Math.floor(diffSec / 3600) + ' saat önce';
  return Math.floor(diffSec / 86400) + ' gün önce';
}

/** Kullanıcı profilini Firestore'dan çekme */
async function fetchUserProfile(uid) {
  if (!uid) return { username: 'Kullanıcı', photoURL: null };
  try {
    const snap = await getDoc(doc(db, 'users', uid));
    if (snap.exists()) {
      const d = snap.data();
      return {
        username: d.username || 'Kullanıcı',
        photoURL: d.photoURL || null
      };
    }
  } catch (e) {
    console.log('fetchUserProfile err:', e);
  }
  return { username: 'Kullanıcı', photoURL: null };
}

/** @mention metnini mavi renkle parse edip gösterir */
function parseAndRenderText(text) {
  if (!text) return null;
  if (typeof text !== 'string') {
    return <Text style={{ color: '#fff', fontSize: 16 }}>{text}</Text>;
  }
  const parts = text.split(/\s+/);
  return (
    <Text style={{ color: '#fff', flexWrap: 'wrap', fontSize: 16 }}>
      {parts.map((part, idx) => {
        if (part.startsWith('@') && part.length > 1) {
          return (
            <Text key={idx} style={{ color: '#3498db', fontSize: 16 }}>
              {part + ' '}
            </Text>
          );
        }
        return part + ' ';
      })}
    </Text>
  );
}

export default function CommentsScreen() {
  const route = useRoute();
  const navigation = useNavigation();
  const { postId } = route.params || {};

  const [comments, setComments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [timeNow, setTimeNow] = useState(Date.now());
  const [refreshing, setRefreshing] = useState(false);

  const [expanded, setExpanded] = useState({});
  const [commentText, setCommentText] = useState('');

  // replyMode: { commentId, username, isNested, replyId }
  const [replyMode, setReplyMode] = useState(null);

  const [showMentionList, setShowMentionList] = useState(false);
  const [mentionQuery, setMentionQuery] = useState('');
  const [mentionUsers, setMentionUsers] = useState([]);
  const mentionTimer = useRef(null);

  const inputRef = useRef(null);
  const flatListRef = useRef(null);

  useEffect(() => {
    if (!postId) {
      setLoading(false);
      return;
    }
    loadCommentsRealtime();
  }, [postId]);

  function loadCommentsRealtime() {
    setLoading(true);
    const cRef = collection(db, 'posts', postId, 'comments');
    const q = query(cRef, orderBy('createdAt', 'asc'));
    const unsub = onSnapshot(q, async (snapshot) => {
      const arr = [];
      for (let ds of snapshot.docs) {
        const d = ds.data();
        const up = await fetchUserProfile(d.uid);
        arr.push({
          id: ds.id,
          postId,
          ...d,
          username: up.username,
          userPhoto: up.photoURL
        });
      }
      setComments(arr);
      setLoading(false);
      setRefreshing(false);

      // Gönderi dokümanındaki yorum sayısını güncelle
      try {
        const postRef = doc(db, 'posts', postId);
        await updateDoc(postRef, {
          commentsCount: snapshot.size
        });

        // Gönderi sahibinin popülerlik puanını güncelle
        const postSnap = await getDoc(postRef);
        if (postSnap.exists()) {
          const postData = postSnap.data();
          await updateUserPopularity(postData.uid, false);
        }
      } catch (error) {
        console.error('Yorum sayısı güncelleme hatası:', error);
      }
    });

    // Gönderi yorum sayısını dinle
    listenToPostComments(postId, (commentCount) => {
      console.log(`Gönderi ${postId} yorum sayısı: ${commentCount}`);
    });

    return () => unsub();
  }

  function onRefresh() {
    setRefreshing(true);
    setTimeNow(Date.now());
    setTimeout(() => setRefreshing(false), 700);
  }

  function handleGoBack() {
    navigation.goBack();
  }

  // @mention algılaması
  useEffect(() => {
    const lastChar = commentText.slice(-1);
    if (lastChar === '@') {
      setShowMentionList(true);
      setMentionQuery('');
    } else if (showMentionList) {
      const parted = commentText.split('@');
      if (parted.length > 1) {
        const afterAt = parted[parted.length - 1];
        const spaceIdx = afterAt.search(/[\s.,:;!?\n]/);
        if (spaceIdx >= 0) {
          setShowMentionList(false);
        } else {
          setMentionQuery(afterAt.toLowerCase());
        }
      } else {
        setShowMentionList(false);
      }
    }
  }, [commentText]);

  useEffect(() => {
    if (!showMentionList) return;
    if (!mentionQuery) {
      setMentionUsers([]);
      return;
    }
    if (mentionTimer.current) {
      clearTimeout(mentionTimer.current);
    }
    mentionTimer.current = setTimeout(() => {
      searchMention(mentionQuery);
    }, 300);
  }, [mentionQuery, showMentionList]);

  function searchMention(q) {
    if (!q) return;
    onSnapshot(collection(db, 'users'), (snap) => {
      let allUsers = snap.docs.map(d => ({ id: d.id, ...d.data() }));
      let filtered = allUsers.filter(u =>
        (u.username || '').toLowerCase().includes(q)
      );
      filtered = filtered.slice(0, MENTION_SUGGEST_LIMIT);
      setMentionUsers(filtered);
    });
  }

  function handleSelectMentionUser(u) {
    const parted = commentText.split('@');
    parted.pop();
    const newTxt = parted.join('@') + '@' + u.username + ' ';
    setCommentText(newTxt);
    setShowMentionList(false);
  }

  // Yorum veya cevaba cevap
  function handleReplyPress(id, username, isNested = false, replyId = null) {
    setReplyMode({ commentId: id, username, isNested, replyId });
    setCommentText('@' + username + ' ');
    if (inputRef.current) {
      setTimeout(() => inputRef.current.focus(), 50);
    }
  }

  async function handleSend() {
    let tx = commentText.trim();
    if (!tx) return;
    if (tx.length > COMMENT_CHAR_LIMIT) {
      tx = tx.substring(0, COMMENT_CHAR_LIMIT) + '...';
    }
    if (replyMode) {
      if (replyMode.isNested) {
        await addNestedReply(replyMode.commentId, replyMode.replyId, tx, replyMode.username);
      } else {
        await addReply(replyMode.commentId, tx, replyMode.username);
      }
      setReplyMode(null);
    } else {
      await addComment(tx);
    }
    setCommentText('');
    Keyboard.dismiss();
  }

  async function addComment(txt) {
    if (!postId) return;
    try {
      const up = await fetchUserProfile(auth.currentUser?.uid);

      // Önce gönderi sahibini bul
      const postRef = doc(db, 'posts', postId);
      const postSnap = await getDoc(postRef);

      if (!postSnap.exists()) {
        console.log('Gönderi bulunamadı');
        return;
      }

      const postData = postSnap.data();
      const postOwnerId = postData.uid;

      // Yorumu ekle
      const commentRef = await addDoc(collection(db, 'posts', postId, 'comments'), {
        uid: auth.currentUser?.uid,
        username: up.username,
        userPhoto: up.photoURL,
        comment: txt,
        createdAt: serverTimestamp(),
        likes: 0,
        likedBy: [],
        edited: false
      });

      // Gönderi dokümanındaki yorum sayısını artır
      await updateDoc(postRef, {
        commentsCount: increment(1)
      });

      // Yorum bildirimi oluştur (kendi gönderisine yorum yapmışsa bildirim gönderme)
      if (auth.currentUser?.uid !== postOwnerId) {
        await createNotification(
          NOTIFICATION_TYPES.COMMENT,
          auth.currentUser?.uid,
          postOwnerId,
          postId,
          null,
          null,
          txt.substring(0, 50) + (txt.length > 50 ? '...' : '')
        );
      }

      // Gönderi yorum sayısını dinle
      listenToPostComments(postId, (commentCount) => {
        console.log(`Gönderi ${postId} yorum sayısı güncellendi: ${commentCount}`);
        // Burada UI'ı güncelleyebilirsiniz
      });

      // Gönderi sahibinin popülerlik puanını güncelle
      try {
        await updateUserPopularity(postOwnerId, true);
      } catch (popError) {
        console.error('Popülerlik güncelleme hatası:', popError);
      }
    } catch (err) {
      console.log('addComment err:', err);
    }
  }

  async function addReply(parentId, txt, parentUsername) {
    if (!postId) return;
    try {
      const up = await fetchUserProfile(auth.currentUser?.uid);

      // Yorum sahibini bul
      const commentRef = doc(db, 'posts', postId, 'comments', parentId);
      const commentSnap = await getDoc(commentRef);

      if (!commentSnap.exists()) {
        console.log('Yorum bulunamadı');
        return;
      }

      const commentData = commentSnap.data();
      const commentOwnerId = commentData.uid;

      // Cevabı ekle
      await addDoc(
        collection(db, 'posts', postId, 'comments', parentId, 'replies'),
        {
          uid: auth.currentUser?.uid,
          username: up.username,
          userPhoto: up.photoURL,
          reply: txt,
          replyToUser: parentUsername,
          createdAt: serverTimestamp(),
          likes: 0,
          likedBy: [],
          edited: false
        }
      );

      // Cevap bildirimi oluştur (kendi yorumuna cevap vermişse bildirim gönderme)
      if (auth.currentUser?.uid !== commentOwnerId) {
        await createNotification(
          NOTIFICATION_TYPES.REPLY,
          auth.currentUser?.uid,
          commentOwnerId,
          postId,
          parentId,
          null,
          txt.substring(0, 50) + (txt.length > 50 ? '...' : '')
        );
      }
    } catch (err) {
      console.log('addReply err:', err);
    }
  }

  async function addNestedReply(commentId, replyId, txt, parentUsername) {
    if (!postId) return;
    try {
      const up = await fetchUserProfile(auth.currentUser?.uid);

      // Cevap sahibini bul
      const replyRef = doc(db, 'posts', postId, 'comments', commentId, 'replies', replyId);
      const replySnap = await getDoc(replyRef);

      if (!replySnap.exists()) {
        console.log('Cevap bulunamadı');
        return;
      }

      const replyData = replySnap.data();
      const replyOwnerId = replyData.uid;

      // Cevabı ekle
      await addDoc(
        collection(db, 'posts', postId, 'comments', commentId, 'replies'),
        {
          uid: auth.currentUser?.uid,
          username: up.username,
          userPhoto: up.photoURL,
          reply: txt,
          replyToUser: parentUsername,
          parentReplyId: replyId,
          createdAt: serverTimestamp(),
          likes: 0,
          likedBy: [],
          edited: false
        }
      );

      // Cevap bildirimi oluştur (kendi cevabına cevap vermişse bildirim gönderme)
      if (auth.currentUser?.uid !== replyOwnerId) {
        await createNotification(
          NOTIFICATION_TYPES.REPLY,
          auth.currentUser?.uid,
          replyOwnerId,
          postId,
          commentId,
          replyId,
          txt.substring(0, 50) + (txt.length > 50 ? '...' : '')
        );
      }
    } catch (err) {
      console.log('addNestedReply err:', err);
    }
  }

  async function toggleCommentLike(commentId) {
    if (!auth.currentUser) return;
    const userId = auth.currentUser.uid;
    setComments(prev =>
      prev.map(c => {
        if (c.id === commentId) {
          const isLiked = c.likedBy?.includes(userId);
          const newLikes = isLiked ? (c.likes || 0) - 1 : (c.likes || 0) + 1;
          const newLikedBy = isLiked
            ? c.likedBy.filter(x => x !== userId)
            : [...(c.likedBy || []), userId];
          return { ...c, likes: newLikes, likedBy: newLikedBy };
        }
        return c;
      })
    );
    try {
      const docRef = doc(db, 'posts', postId, 'comments', commentId);
      const snap = await getDoc(docRef);
      if (!snap.exists()) return;
      const data = snap.data();
      const isLiked = data.likedBy?.includes(userId);
      await updateDoc(docRef, {
        likes: isLiked ? data.likes - 1 : data.likes + 1,
        likedBy: isLiked
          ? data.likedBy.filter(x => x !== userId)
          : [...(data.likedBy || []), userId]
      });
    } catch (err) {
      console.log('toggleCommentLike err:', err);
    }
  }

  function handleShowCommentLikers(comment) {
    if (!comment.likedBy || comment.likedBy.length === 0) {
      Alert.alert('Bilgi', 'Bu yorumu beğenen yok.');
      return;
    }
    navigation.navigate('LikersScreen', { likedBy: comment.likedBy });
  }

  function handleEditRequest(c) {
    if (c.uid !== auth.currentUser?.uid) {
      Alert.alert('Hata', 'Sadece kendi yorumunuzu düzenleyebilirsiniz.');
      return;
    }
    setComments(prev =>
      prev.map(x => {
        if (x.id === c.id) {
          return { ...x, editMode: true, editText: x.comment };
        }
        return x;
      })
    );
  }

  function handleCancelEdit(commentId) {
    setComments(prev =>
      prev.map(x => {
        if (x.id === commentId) {
          const { editMode, editText, ...rest } = x;
          return rest;
        }
        return x;
      })
    );
  }

  async function handleSaveEdit(commentId) {
    const c = comments.find(x => x.id === commentId);
    if (!c) return;
    let newText = (c.editText || '').trim();
    if (!newText) {
      Alert.alert('Uyarı', 'Boş yorum olamaz.');
      return;
    }
    if (newText.length > COMMENT_CHAR_LIMIT) {
      newText = newText.substring(0, COMMENT_CHAR_LIMIT) + '...';
    }
    try {
      await updateDoc(doc(db, 'posts', postId, 'comments', commentId), {
        comment: newText,
        edited: true
      });
      setComments(prev =>
        prev.map(y => {
          if (y.id === commentId) {
            return {
              ...y,
              comment: newText,
              edited: true,
              editMode: false,
              editText: undefined
            };
          }
          return y;
        })
      );
    } catch (err) {
      Alert.alert('Hata', 'Düzenleme başarısız: ' + err.message);
    }
  }

  async function handleDeleteComment(c) {
    if (c.uid !== auth.currentUser?.uid) {
      Alert.alert('Hata', 'Sadece kendi yorumunuzu silebilirsiniz.');
      return;
    }
    Alert.alert('Sil', 'Bu yorumu silmek istiyor musunuz?', [
      { text: 'İptal', style: 'cancel' },
      {
        text: 'Sil',
        style: 'destructive',
        onPress: async () => {
          try {
            await deleteDoc(doc(db, 'posts', postId, 'comments', c.id));
          } catch (err) {
            console.log('deleteComment err:', err);
          }
        }
      }
    ]);
  }

  function toggleReplies(commentId) {
    setExpanded(prev => {
      const newVal = { ...prev, [commentId]: !prev[commentId] };
      // Replies açıldığında otomatik en alta kaydırmak isterseniz:
      // if (newVal[commentId] && flatListRef.current) {
      //   setTimeout(() => {
      //     flatListRef.current.scrollToEnd({ animated: true });
      //   }, 100);
      // }
      return newVal;
    });
  }

  function renderItem({ item }) {
    return (
      <CommentItem
        comment={item}
        expanded={expanded}
        toggleReplies={toggleReplies}
        handleReplyPress={handleReplyPress}
        toggleCommentLike={toggleCommentLike}
        handleShowCommentLikers={handleShowCommentLikers}
        handleEditRequest={handleEditRequest}
        handleCancelEdit={handleCancelEdit}
        handleSaveEdit={handleSaveEdit}
        handleDeleteComment={handleDeleteComment}
        timeNow={timeNow}
      />
    );
  }

  if (!postId) {
    return (
      <SafeAreaView style={[styles.container, { justifyContent: 'center', alignItems: 'center' }]}>
        <Text style={{ color: '#fff' }}>Gönderi Bulunamadı.</Text>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Tüm ekranı KeyboardAvoidingView ile sarmalıyoruz */}
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 100 : 0}
      >
        {/* Boş yere tıklayınca klavyeyi kapatmak için */}
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
          <View style={{ flex: 1 }}>
            {/* Üst Header */}
            <View style={styles.topHeader}>
              <TouchableOpacity onPress={handleGoBack} style={{ marginRight: 10 }}>
                <Ionicons name="arrow-back" size={24} color="#fff" />
              </TouchableOpacity>
              <Text style={styles.headerTitle}>Yorumlar</Text>
              <View style={{ width: 24 }} />
            </View>

            {/* İçerik (FlatList + mentionList + replyBanner) */}
            {loading ? (
              <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
                <ActivityIndicator size="large" color="#fff" />
              </View>
            ) : (
              <View style={{ flex: 1 }}>
                <FlatList
                  ref={flatListRef}
                  data={comments}
                  keyExtractor={(item) => item.id}
                  renderItem={renderItem}
                  keyboardShouldPersistTaps="always"
                  refreshControl={
                    <RefreshControl refreshing={refreshing} onRefresh={onRefresh} tintColor="#fff" />
                  }
                  ListEmptyComponent={
                    <Text style={styles.emptyListText}>Henüz yorum yok.</Text>
                  }
                  // Alt tarafta input bar'ın kaplaması için biraz boşluk
                  contentContainerStyle={{ paddingBottom: 80 }}
                />

                {/* @mention listesi */}
                {showMentionList && mentionUsers.length > 0 && (
                  <MentionList mentionUsers={mentionUsers} onSelect={handleSelectMentionUser} />
                )}

                {/* Cevap Banner */}
                {replyMode && (
                  <View style={styles.replyBanner}>
                    <Text style={styles.replyBannerText}>
                      {replyMode.isNested
                        ? `@${replyMode.username} adlı cevaba cevap`
                        : `@${replyMode.username} adlı kişiye cevap`}
                    </Text>
                    <TouchableOpacity
                      onPress={() => {
                        setReplyMode(null);
                        setCommentText('');
                      }}
                    >
                      <Ionicons name="close-circle" size={20} color="#fff" />
                    </TouchableOpacity>
                  </View>
                )}
              </View>
            )}

            {/* Alt kısımdaki Yorum Yaz Input */}
            <View style={styles.inputBar}>
              <TextInput
                ref={inputRef}
                style={styles.input}
                placeholder={
                  replyMode
                    ? replyMode.isNested
                      ? 'Cevap yaz (nested)...'
                      : 'Cevap yaz...'
                    : 'Yorum yaz...'
                }
                placeholderTextColor="#aaa"
                multiline
                value={commentText}
                onChangeText={setCommentText}
              />
              <TouchableOpacity style={styles.sendBtn} onPress={handleSend}>
                <Ionicons name="send" size={20} color="#fff" />
              </TouchableOpacity>
            </View>
          </View>
        </TouchableWithoutFeedback>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

/** Tekil Yorum Bileşeni */
function CommentItem({
  comment,
  expanded,
  toggleReplies,
  handleReplyPress,
  toggleCommentLike,
  handleShowCommentLikers,
  handleEditRequest,
  handleCancelEdit,
  handleSaveEdit,
  handleDeleteComment,
  timeNow
}) {
  const [deleted, setDeleted] = useState(false);
  const panX = useRef(new Animated.Value(0)).current;
  const navigation = useNavigation();

  const isOwner = comment.uid === auth.currentUser?.uid;
  const isLiked = comment.likedBy?.includes(auth.currentUser?.uid);
  const editMode = comment.editMode || false;
  const [editText, setEditText] = useState(comment.editText || '');

  const [repliesCount, setRepliesCount] = useState(0);
  useEffect(() => {
    if (!comment.id || !comment.postId) return;
    const rRef = collection(db, 'posts', comment.postId, 'comments', comment.id, 'replies');
    const q = query(rRef, orderBy('createdAt', 'asc'));
    const unsub = onSnapshot(q, snap => {
      setRepliesCount(snap.size);
    });
    return () => unsub();
  }, [comment.id, comment.postId]);

  useEffect(() => {
    if (editMode) {
      setEditText(comment.editText || comment.comment || '');
    }
  }, [editMode]);

  const panResponder = useRef(
    PanResponder.create({
      onMoveShouldSetPanResponder: (_, g) => isOwner && Math.abs(g.dx) > 15,
      onPanResponderMove: (_, g) => {
        panX.setValue(g.dx);
      },
      onPanResponderRelease: async (_, g) => {
        if (g.dx < -120 && isOwner) {
          LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
          setDeleted(true);
          await handleDeleteComment(comment);
        } else {
          Animated.spring(panX, {
            toValue: 0,
            useNativeDriver: true
          }).start();
        }
      }
    })
  ).current;
  if (deleted) return null;

  const timeTxt = comment.createdAt ? timeAgo(comment.createdAt, timeNow) : '';
  const showEditedLabel = comment.edited && isOwner && !editMode;

  function goProfile() {
    navigation.navigate('OtherProfileScreen', { uid: comment.uid });
  }

  function handleSave() {
    if (!editText.trim()) {
      Alert.alert('Uyarı', 'Boş yorum olamaz');
      return;
    }
    comment.editText = editText;
    handleSaveEdit(comment.id);
  }

  return (
    <Animated.View
      style={[styles.commentBox, { transform: [{ translateX: panX }] }]}
      {...(isOwner ? panResponder.panHandlers : {})}
    >
      <View style={styles.commentHeader}>
        <TouchableOpacity style={styles.commentLeft} onPress={goProfile}>
          <Image
            source={
              comment.userPhoto
                ? { uri: comment.userPhoto }
                : require('../assets/default-avatar.png')
            }
            style={styles.avatar}
          />
          <Text style={styles.username}>{comment.username}</Text>
        </TouchableOpacity>
        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
          {showEditedLabel && <Text style={styles.editedLabel}>Düzenlendi</Text>}
          {isOwner && !editMode && (
            <>
              <TouchableOpacity
                style={{ marginHorizontal: 10 }}
                onPress={() => handleEditRequest(comment)}
              >
                <Ionicons name="create-outline" size={18} color="#fff" />
              </TouchableOpacity>
              <TouchableOpacity onPress={() => handleDeleteComment(comment)}>
                <Ionicons name="trash-outline" size={18} color="#f44" />
              </TouchableOpacity>
            </>
          )}
        </View>
      </View>

      {editMode ? (
        <View style={styles.editContainer}>
          <TextInput
            style={styles.editInput}
            value={editText}
            multiline
            onChangeText={(val) => {
              if (val.length > COMMENT_CHAR_LIMIT) {
                val = val.substring(0, COMMENT_CHAR_LIMIT) + '...';
              }
              setEditText(val);
            }}
          />
          <View style={styles.editButtonsRow}>
            <TouchableOpacity
              style={styles.cancelBtn}
              onPress={() => handleCancelEdit(comment.id)}
            >
              <Ionicons name="close" size={16} color="#fff" style={{ marginRight: 4 }} />
              <Text style={styles.cancelBtnText}>Vazgeç</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.saveBtn} onPress={handleSave}>
              <Ionicons name="checkmark-done" size={16} color="#fff" style={{ marginRight: 4 }} />
              <Text style={styles.saveBtnText}>Kaydet</Text>
            </TouchableOpacity>
          </View>
        </View>
      ) : (
        <View style={styles.commentContent}>
          {parseAndRenderText(comment.comment)}
        </View>
      )}

      <View style={[styles.commentFooter, { justifyContent: 'space-between' }]}>
        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
          <TouchableOpacity
            style={styles.actionBtn}
            onPress={() => toggleCommentLike(comment.id)}
            onLongPress={() => handleShowCommentLikers(comment)}
          >
            <Ionicons
              name={isLiked ? 'heart' : 'heart-outline'}
              size={22}
              color={isLiked ? '#e74c3c' : '#fff'}
            />
            <Text style={styles.actionLabel}>{comment.likes || 0}</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.actionBtn}
            onPress={() => handleReplyPress(comment.id, comment.username)}
          >
            <Ionicons name="arrow-undo-outline" size={22} color="#fff" />
            <Text style={styles.actionLabel}>Cevap</Text>
          </TouchableOpacity>
          {repliesCount > 0 && (
            <TouchableOpacity style={styles.toggleRepliesBtn} onPress={() => toggleReplies(comment.id)}>
              <Text style={styles.toggleRepliesText}>
                {expanded[comment.id] ? 'Cevapları Gizle' : `${repliesCount} yanıtı görüntüle`}
              </Text>
            </TouchableOpacity>
          )}
        </View>
        <Text style={styles.timeText}>{timeTxt}</Text>
      </View>

      <RepliesPreview
        parentComment={comment}
        expanded={expanded[comment.id]}
        toggleReplies={toggleReplies}
        timeNow={timeNow}
        handleReplyPress={handleReplyPress}
      />
    </Animated.View>
  );
}

/** RepliesPreview: alt cevapları yükler */
function RepliesPreview({ parentComment, expanded, toggleReplies, timeNow, handleReplyPress }) {
  const { id: parentId, postId } = parentComment || {};
  const [replies, setReplies] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (!postId || !parentId) {
      setLoading(false);
      return;
    }
    const rRef = collection(db, 'posts', postId, 'comments', parentId, 'replies');
    const q = query(rRef, orderBy('createdAt', 'asc'));
    const unsub = onSnapshot(q, async snap => {
      const arr = [];
      for (let ds of snap.docs) {
        const dd = ds.data();
        const up = await fetchUserProfile(dd.uid);
        arr.push({
          id: ds.id,
          ...dd,
          username: up.username,
          userPhoto: up.photoURL
        });
      }
      setReplies(arr);
      setLoading(false);
    });
    return () => unsub();
  }, [postId, parentId]);

  if (loading) {
    return <ActivityIndicator color="#fff" style={{ marginTop: 5 }} />;
  }
  if (replies.length === 0) {
    return null;
  }
  if (!expanded) {
    return null;
  }
  return (
    <RepliesList
      postId={postId}
      parentId={parentId}
      replies={replies}
      toggleReplies={toggleReplies}
      timeNow={timeNow}
      handleReplyPress={handleReplyPress}
    />
  );
}

/** RepliesList: Yorum altındaki tüm cevaplar */
function RepliesList({ postId, parentId, replies, toggleReplies, timeNow, handleReplyPress }) {
  const [localReplies, setLocalReplies] = useState(replies);
  const navigation = useNavigation();

  useEffect(() => {
    setLocalReplies(replies);
  }, [replies]);

  async function toggleReplyLike(replyId) {
    if (!auth.currentUser) return;
    const userId = auth.currentUser.uid;
    setLocalReplies(prev =>
      prev.map(r => {
        if (r.id === replyId) {
          const isLiked = r.likedBy?.includes(userId);
          return {
            ...r,
            likes: isLiked ? (r.likes || 0) - 1 : (r.likes || 0) + 1,
            likedBy: isLiked
              ? r.likedBy.filter(x => x !== userId)
              : [...(r.likedBy || []), userId]
          };
        }
        return r;
      })
    );
    try {
      const ref = doc(db, 'posts', postId, 'comments', parentId, 'replies', replyId);
      const snap = await getDoc(ref);
      if (!snap.exists()) return;
      const d = snap.data();
      const isLiked = d.likedBy?.includes(userId);
      await updateDoc(ref, {
        likes: isLiked ? d.likes - 1 : d.likes + 1,
        likedBy: isLiked
          ? d.likedBy.filter(x => x !== userId)
          : [...(d.likedBy || []), userId]
      });
    } catch (err) {
      console.log('toggleReplyLike err:', err);
    }
  }

  function handleShowReplyLikers(reply) {
    if (!reply.likedBy || reply.likedBy.length === 0) {
      Alert.alert('Bilgi', 'Bu cevabı beğenen yok.');
      return;
    }
    navigation.navigate('LikersScreen', { likedBy: reply.likedBy });
  }

  async function handleDeleteReply(item) {
    if (item.uid !== auth.currentUser?.uid) {
      Alert.alert('Hata', 'Sadece kendi cevabınızı silebilirsiniz.');
      return;
    }
    Alert.alert('Sil', 'Bu cevabı silmek istiyor musunuz?', [
      { text: 'İptal', style: 'cancel' },
      {
        text: 'Sil',
        style: 'destructive',
        onPress: async () => {
          try {
            await deleteDoc(doc(db, 'posts', postId, 'comments', parentId, 'replies', item.id));
            setLocalReplies(prev => prev.filter(x => x.id !== item.id));
          } catch (e) {
            console.log('deleteReply err:', e);
          }
        }
      }
    ]);
  }

  function goReplyUserProfile(uid) {
    navigation.navigate('OtherProfileScreen', { uid });
  }

  function handleNestedReply(replyItem) {
    handleReplyPress(parentId, replyItem.username, true, replyItem.id);
  }

  if (localReplies.length === 0) return null;

  return (
    <View style={{ marginTop: 8, marginLeft: 35 }}>
      {localReplies.map(r => {
        const isLiked = r.likedBy?.includes(auth.currentUser?.uid);
        const dateText = r.createdAt ? timeAgo(r.createdAt, timeNow) : '';
        const editedSuffix = r.edited ? ' (düzenlendi)' : '';
        let disp = r.reply || '';
        if (disp.length > COMMENT_CHAR_LIMIT) {
          disp = disp.substring(0, COMMENT_CHAR_LIMIT) + '...';
        }
        return (
          <View key={r.id} style={styles.singleReply}>
            <TouchableOpacity onPress={() => goReplyUserProfile(r.uid)}>
              <Image
                source={
                  r.userPhoto
                    ? { uri: r.userPhoto }
                    : require('../assets/default-avatar.png')
                }
                style={styles.replyAvatar}
              />
            </TouchableOpacity>
            <View style={{ flex: 1 }}>
              <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                <TouchableOpacity onPress={() => goReplyUserProfile(r.uid)}>
                  <Text style={[styles.username, { fontSize: 15 }]}>{r.username}</Text>
                </TouchableOpacity>
                {r.uid === auth.currentUser?.uid && (
                  <TouchableOpacity onPress={() => handleDeleteReply(r)}>
                    <Ionicons name="trash-outline" size={16} color="#f44" />
                  </TouchableOpacity>
                )}
              </View>
              <View style={{ marginTop: 2 }}>
                {parseAndRenderText(disp)}
              </View>
              <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginTop: 4 }}>
                <View style={{ flexDirection: 'row' }}>
                  <TouchableOpacity
                    style={styles.replyActionBtn}
                    onPress={() => toggleReplyLike(r.id)}
                    onLongPress={() => handleShowReplyLikers(r)}
                  >
                    <Ionicons
                      name={isLiked ? 'heart' : 'heart-outline'}
                      size={18}
                      color={isLiked ? '#e74c3c' : '#fff'}
                    />
                    <Text style={styles.replyActionText}>{r.likes || 0}</Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={styles.replyActionBtn}
                    onPress={() => handleNestedReply(r)}
                  >
                    <Ionicons name="arrow-undo-outline" size={18} color="#fff" />
                    <Text style={styles.replyActionText}>Cevapla</Text>
                  </TouchableOpacity>
                </View>
                <Text style={styles.replyTime}>{dateText + editedSuffix}</Text>
              </View>
            </View>
          </View>
        );
      })}
    </View>
  );
}

/** @mention listesi */
function MentionList({ mentionUsers, onSelect }) {
  return (
    <View style={styles.mentionContainer}>
      <ScrollView>
        {mentionUsers.map(u => (
          <TouchableOpacity
            key={u.id}
            style={styles.mentionItem}
            onPress={() => onSelect(u)}
          >
            <Image
              source={
                u.photoURL
                  ? { uri: u.photoURL }
                  : require('../assets/default-avatar.png')
              }
              style={styles.mentionAvatar}
            />
            <Text style={styles.mentionUsername}>{u.username}</Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000'
  },
  topHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    height: 50,
    borderBottomColor: '#333',
    borderBottomWidth: 0.5,
    backgroundColor: '#000',
    paddingHorizontal: 10
  },
  headerTitle: {
    flex: 1,
    fontSize: 18,
    fontWeight: '600',
    color: '#fff',
    textAlign: 'center'
  },
  emptyListText: {
    color: '#999',
    textAlign: 'center',
    marginTop: 30,
    fontSize: 15
  },
  commentBox: {
    backgroundColor: '#111',
    borderRadius: 8,
    padding: 10,
    marginHorizontal: 10,
    marginVertical: 5
  },
  commentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center'
  },
  commentLeft: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  avatar: {
    width: 38,
    height: 38,
    borderRadius: 19,
    borderWidth: 1,
    borderColor: '#333',
    marginRight: 8
  },
  username: {
    color: '#fff',
    fontSize: 15,
    fontWeight: 'bold'
  },
  editedLabel: {
    color: '#ccc',
    fontSize: 13,
    marginRight: 8,
    fontStyle: 'italic'
  },
  editContainer: {
    backgroundColor: '#222',
    borderRadius: 6,
    padding: 8,
    marginTop: 6
  },
  editInput: {
    backgroundColor: '#333',
    color: '#fff',
    borderRadius: 4,
    fontSize: 15,
    padding: 6,
    minHeight: 40
  },
  editButtonsRow: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 8
  },
  cancelBtn: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#444',
    borderRadius: 4,
    paddingHorizontal: 10,
    paddingVertical: 6,
    marginRight: 8
  },
  cancelBtnText: {
    color: '#fff',
    fontSize: 14
  },
  saveBtn: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#0d6efd',
    borderRadius: 4,
    paddingHorizontal: 10,
    paddingVertical: 6
  },
  saveBtnText: {
    color: '#fff',
    fontSize: 14
  },
  commentContent: {
    marginTop: 6
  },
  commentFooter: {
    flexDirection: 'row',
    marginTop: 8,
    alignItems: 'center'
  },
  actionBtn: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 20
  },
  actionLabel: {
    color: '#fff',
    marginLeft: 5,
    fontSize: 15
  },
  timeText: {
    color: '#aaa',
    fontSize: 13
  },
  toggleRepliesBtn: {
    justifyContent: 'center',
    marginLeft: 10
  },
  toggleRepliesText: {
    color: '#3498db',
    fontSize: 14
  },
  // Reply stilleri
  singleReply: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: '#1a1a1a',
    borderRadius: 6,
    padding: 8,
    marginBottom: 4
  },
  replyAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: '#444',
    marginRight: 6
  },
  replyActionBtn: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 18
  },
  replyActionText: {
    color: '#fff',
    marginLeft: 4,
    fontSize: 14
  },
  replyTime: {
    color: '#aaa',
    fontSize: 12
  },
  // Alt Input (artık absolute değil!)
  inputBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#000',
    borderTopColor: '#333',
    borderTopWidth: 0.5,
    padding: 8
  },
  input: {
    flex: 1,
    backgroundColor: '#1a1a1a',
    color: '#fff',
    borderRadius: 6,
    paddingHorizontal: 10,
    paddingVertical: 6,
    fontSize: 15,
    maxHeight: 120
  },
  sendBtn: {
    backgroundColor: '#0066CC',
    marginLeft: 8,
    padding: 10,
    borderRadius: 20
  },
  // Cevap Banner
  replyBanner: {
    backgroundColor: '#222',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 10,
    paddingVertical: 6
  },
  replyBannerText: {
    color: '#fff',
    fontSize: 14,
    marginRight: 10
  },
  // @mention listesi
  mentionContainer: {
    position: 'absolute',
    bottom: 60, // Gerekirse ayarlayabilirsiniz
    left: 10,
    right: 10,
    maxHeight: 220,
    backgroundColor: '#111',
    borderColor: '#333',
    borderWidth: 1,
    borderRadius: 6,
    zIndex: 9999
  },
  mentionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    borderBottomColor: '#333',
    borderBottomWidth: 1
  },
  mentionAvatar: {
    width: 30,
    height: 30,
    borderRadius: 15,
    marginRight: 8,
    borderWidth: 1,
    borderColor: '#444'
  },
  mentionUsername: {
    color: '#fff',
    fontSize: 14
  }
});
