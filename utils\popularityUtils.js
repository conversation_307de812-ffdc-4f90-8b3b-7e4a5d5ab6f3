import {
  doc,
  getDoc,
  updateDoc,
  collection,
  getDocs,
  query,
  where,
  onSnapshot,
  serverTimestamp,
  writeBatch,
  increment,
  collectionGroup,
  orderBy,
  limit
} from 'firebase/firestore';
import { db } from '../firebase';

// Aktif dinleyicileri takip etmek için
const activeListeners = {
  users: {},
  posts: {},
  comments: {},
  leaderboard: {}
};

// Global popülerlik değerlerini saklamak için
const globalPopularityData = {
  allTimePopular: [],
  dailyStars: [],
  hourlyTrending: [],
  mostFollowed: [],
  lastUpdated: 0
};

/**
 * Kullanıcının popülerlik puanını hesaplar ve Firestore'da günceller
 * @param {string} userId - Popülerliği hesaplanacak kullanıcının ID'si
 * @param {boolean} forceUpdate - Zorla güncelleme yapılsın mı
 */
export const updateUserPopularity = async (userId, forceUpdate = false) => {
  try {
    if (!userId) {
      return null;
    }

    // <PERSON>llanıcı verisini al
    const userRef = doc(db, 'users', userId);

    // Optimistik güncelleme: Önce mevcut popülerliği döndür, sonra arka planda güncelle
    getDoc(userRef).then(userSnap => {
      if (!userSnap.exists()) {
        return;
      }

      const userData = userSnap.data();

      // Son güncelleme zamanını kontrol et (son 2 saniye içinde güncellenmişse ve zorla güncelleme istenmemişse atla)
      const lastUpdate = userData.lastPopularityUpdate?.toDate?.();
      const now = new Date();
      if (!forceUpdate && lastUpdate && (now - lastUpdate) < 2000) {
        return;
      }

      // Paralel sorgular
      Promise.all([
        // 1. Kullanıcının gönderilerini al
        getDocs(query(collection(db, 'posts'), where('uid', '==', userId))),

        // 2. Kullanıcının yorumlarını al (isteğe bağlı, performans için kaldırılabilir)
        // getDocs(query(collectionGroup(db, 'comments'), where('uid', '==', userId)))
      ]).then(([postsSnap]) => {
        const posts = postsSnap.docs.map(doc => ({ id: doc.id, ...doc.data() }));

        // Beğeni sayısını hesapla
        let totalLikes = 0;
        posts.forEach(post => {
          totalLikes += post.likes || 0;
        });

        // Takipçi sayısını al
        const followersCount = userData.followers?.length || 0;

        // Popülerlik puanı hesaplama: 10 beğeni = 1 puan, 3 yorum = 1 puan, 1 takipçi = 1 puan
        const likesPoints = Math.floor(totalLikes / 10);
        const commentsPoints = Math.floor((userData.commentsCount || 0) / 3); // Mevcut yorum sayısını kullan
        const followersPoints = followersCount;

        const popularityScore = likesPoints + commentsPoints + followersPoints;

        // Zaman sınırları
        const oneDayAgo = new Date(now.getTime() - (24 * 60 * 60 * 1000));
        const oneHourAgo = new Date(now.getTime() - (60 * 60 * 1000));

        // Son 24 saat ve son 1 saatteki gönderileri filtrele
        const dailyPosts = posts.filter(post => {
          const postDate = post.createdAt?.toDate?.() || new Date();
          return postDate >= oneDayAgo;
        });

        const hourlyPosts = posts.filter(post => {
          const postDate = post.createdAt?.toDate?.() || new Date();
          return postDate >= oneHourAgo;
        });

        // Günlük ve saatlik beğeni sayılarını hesapla
        let dailyLikes = 0;
        dailyPosts.forEach(post => {
          dailyLikes += post.likes || 0;
        });

        // Saatlik beğeni sayılarını hesapla
        let hourlyLikes = 0;
        hourlyPosts.forEach(post => {
          hourlyLikes += post.likes || 0;
        });

        // Günlük ve saatlik popülerlik puanlarını hesapla (yorum sayısı için mevcut değerleri kullan)
        const dailyPopularity = Math.floor(dailyLikes / 10) + Math.floor((userData.commentsCount || 0) / 3) + followersPoints;
        const hourlyPopularity = Math.floor(hourlyLikes / 10) + Math.floor((userData.commentsCount || 0) / 3) + followersPoints;

        // Kullanıcı dokümanını güncelle
        updateDoc(userRef, {
          popularity: popularityScore,
          dailyPopularity: dailyPopularity,
          hourlyPopularity: hourlyPopularity,
          likesCount: totalLikes,
          followerCount: followersCount,
          lastPopularityUpdate: serverTimestamp()
        });
      });
    });

    // Hemen mevcut popülerliği döndür (optimistik)
    return getDoc(userRef).then(snap => {
      if (snap.exists()) {
        return snap.data().popularity || 0;
      }
      return 0;
    });
  } catch (error) {
    return null;
  }
};

/**
 * Kullanıcının popülerlik değişimlerini dinler
 * @param {string} userId - Dinlenecek kullanıcının ID'si
 * @param {function} callback - Popülerlik değiştiğinde çağrılacak fonksiyon
 * @returns {function} - Dinlemeyi durdurmak için çağrılacak fonksiyon
 */
export const listenToUserPopularity = (userId, callback) => {
  if (!userId) return () => {};

  // Zaten dinleniyor mu kontrol et
  if (activeListeners.users[userId]) {
    return activeListeners.users[userId].addCallback(callback);
  }

  // Yeni dinleyici oluştur
  const userRef = doc(db, 'users', userId);
  const unsubscribe = onSnapshot(userRef, (doc) => {
    if (doc.exists()) {
      const userData = doc.data();
      const popularity = userData.popularity || 0;

      // Tüm callback'leri çağır
      if (activeListeners.users[userId]) {
        activeListeners.users[userId].callbacks.forEach(cb => cb(popularity));
      }
    }
  }, (error) => {
    console.error('Popülerlik dinleme hatası:', error);
  });

  // Dinleyiciyi kaydet
  activeListeners.users[userId] = {
    unsubscribe,
    callbacks: [callback],
    addCallback: (cb) => {
      if (!activeListeners.users[userId].callbacks.includes(cb)) {
        activeListeners.users[userId].callbacks.push(cb);
      }
      return () => {
        activeListeners.users[userId].callbacks =
          activeListeners.users[userId].callbacks.filter(c => c !== cb);

        // Callback kalmadıysa dinlemeyi durdur
        if (activeListeners.users[userId].callbacks.length === 0) {
          activeListeners.users[userId].unsubscribe();
          delete activeListeners.users[userId];
        }
      };
    }
  };

  // Dinlemeyi durdurmak için fonksiyon döndür
  return () => {
    if (activeListeners.users[userId]) {
      activeListeners.users[userId].callbacks =
        activeListeners.users[userId].callbacks.filter(cb => cb !== callback);

      // Callback kalmadıysa dinlemeyi durdur
      if (activeListeners.users[userId].callbacks.length === 0) {
        activeListeners.users[userId].unsubscribe();
        delete activeListeners.users[userId];
      }
    }
  };
};

/**
 * Liderlik tablosunu canlı olarak dinler
 * @param {function} callback - Liderlik tablosu değiştiğinde çağrılacak fonksiyon
 * @returns {function} - Dinlemeyi durdurmak için çağrılacak fonksiyon
 */
export const listenToLeaderboard = (callback) => {
  // Zaten dinleniyor mu kontrol et
  if (activeListeners.leaderboard.main) {
    // Hemen mevcut verileri gönder
    if (globalPopularityData.lastUpdated > 0) {
      setTimeout(() => {
        callback({
          allTimePopular: globalPopularityData.allTimePopular,
          dailyStars: globalPopularityData.dailyStars,
          hourlyTrending: globalPopularityData.hourlyTrending,
          mostFollowed: globalPopularityData.mostFollowed
        });
      }, 0);
    }
    return activeListeners.leaderboard.main.addCallback(callback);
  }

  // İlk yükleme için hemen verileri getir
  const loadInitialData = async () => {
    try {
      // Tüm zamanların en popüler kullanıcıları
      const allTimeSnapshot = await getDocs(
        query(collection(db, 'users'), orderBy('popularity', 'desc'), limit(10))
      );

      // Günün yıldızları
      const dailySnapshot = await getDocs(
        query(collection(db, 'users'), orderBy('dailyPopularity', 'desc'), limit(10))
      );

      // Saatin trendleri
      const hourlySnapshot = await getDocs(
        query(collection(db, 'users'), orderBy('hourlyPopularity', 'desc'), limit(10))
      );

      // En çok takip edilenler
      const followedSnapshot = await getDocs(
        query(collection(db, 'users'), orderBy('followerCount', 'desc'), limit(10))
      );

      // Verileri işle
      globalPopularityData.allTimePopular = allTimeSnapshot.docs.map(doc => ({
        uid: doc.id,
        ...doc.data()
      }));

      globalPopularityData.dailyStars = dailySnapshot.docs.map(doc => ({
        uid: doc.id,
        ...doc.data()
      }));

      globalPopularityData.hourlyTrending = hourlySnapshot.docs.map(doc => ({
        uid: doc.id,
        ...doc.data()
      }));

      globalPopularityData.mostFollowed = followedSnapshot.docs.map(doc => ({
        uid: doc.id,
        ...doc.data()
      }));

      globalPopularityData.lastUpdated = Date.now();

      // Callback'i çağır
      callback({
        allTimePopular: globalPopularityData.allTimePopular,
        dailyStars: globalPopularityData.dailyStars,
        hourlyTrending: globalPopularityData.hourlyTrending,
        mostFollowed: globalPopularityData.mostFollowed
      });
    } catch (error) {
      // Sessizce hata yönetimi
    }
  };

  // İlk verileri yükle
  loadInitialData();

  // Sorguları oluştur
  const allTimeQuery = query(
    collection(db, 'users'),
    orderBy('popularity', 'desc'),
    limit(10)
  );

  const dailyQuery = query(
    collection(db, 'users'),
    orderBy('dailyPopularity', 'desc'),
    limit(10)
  );

  const hourlyQuery = query(
    collection(db, 'users'),
    orderBy('hourlyPopularity', 'desc'),
    limit(10)
  );

  const followedQuery = query(
    collection(db, 'users'),
    orderBy('followerCount', 'desc'),
    limit(10)
  );

  // Veri değişikliklerini bildirmek için debounce mekanizması
  let debounceTimeout = null;
  const notifyCallbacks = () => {
    if (debounceTimeout) {
      clearTimeout(debounceTimeout);
    }

    debounceTimeout = setTimeout(() => {
      if (activeListeners.leaderboard.main) {
        activeListeners.leaderboard.main.callbacks.forEach(cb => cb({
          allTimePopular: globalPopularityData.allTimePopular,
          dailyStars: globalPopularityData.dailyStars,
          hourlyTrending: globalPopularityData.hourlyTrending,
          mostFollowed: globalPopularityData.mostFollowed
        }));
      }
      debounceTimeout = null;
    }, 100); // 100ms debounce
  };

  // Dinleyicileri oluştur
  const unsubscribeAllTime = onSnapshot(allTimeQuery, (snapshot) => {
    const users = snapshot.docs.map(doc => ({
      uid: doc.id,
      ...doc.data()
    }));

    globalPopularityData.allTimePopular = users;
    globalPopularityData.lastUpdated = Date.now();
    notifyCallbacks();
  }, () => {
    // Sessizce hata yönetimi
  });

  const unsubscribeDaily = onSnapshot(dailyQuery, (snapshot) => {
    const users = snapshot.docs.map(doc => ({
      uid: doc.id,
      ...doc.data()
    }));

    globalPopularityData.dailyStars = users;
    globalPopularityData.lastUpdated = Date.now();
    notifyCallbacks();
  }, () => {
    // Sessizce hata yönetimi
  });

  const unsubscribeHourly = onSnapshot(hourlyQuery, (snapshot) => {
    const users = snapshot.docs.map(doc => ({
      uid: doc.id,
      ...doc.data()
    }));

    globalPopularityData.hourlyTrending = users;
    globalPopularityData.lastUpdated = Date.now();
    notifyCallbacks();
  }, () => {
    // Sessizce hata yönetimi
  });

  const unsubscribeFollowed = onSnapshot(followedQuery, (snapshot) => {
    const users = snapshot.docs.map(doc => ({
      uid: doc.id,
      ...doc.data()
    }));

    globalPopularityData.mostFollowed = users;
    globalPopularityData.lastUpdated = Date.now();
    notifyCallbacks();
  }, () => {
    // Sessizce hata yönetimi
  });

  // Dinleyiciyi kaydet
  activeListeners.leaderboard.main = {
    unsubscribe: () => {
      if (debounceTimeout) {
        clearTimeout(debounceTimeout);
        debounceTimeout = null;
      }
      unsubscribeAllTime();
      unsubscribeDaily();
      unsubscribeHourly();
      unsubscribeFollowed();
    },
    callbacks: [callback],
    addCallback: (cb) => {
      if (!activeListeners.leaderboard.main.callbacks.includes(cb)) {
        activeListeners.leaderboard.main.callbacks.push(cb);

        // Hemen mevcut verileri gönder
        if (globalPopularityData.lastUpdated > 0) {
          setTimeout(() => {
            cb({
              allTimePopular: globalPopularityData.allTimePopular,
              dailyStars: globalPopularityData.dailyStars,
              hourlyTrending: globalPopularityData.hourlyTrending,
              mostFollowed: globalPopularityData.mostFollowed
            });
          }, 0);
        }
      }

      return () => {
        activeListeners.leaderboard.main.callbacks =
          activeListeners.leaderboard.main.callbacks.filter(c => c !== cb);

        // Callback kalmadıysa dinlemeyi durdur
        if (activeListeners.leaderboard.main.callbacks.length === 0) {
          activeListeners.leaderboard.main.unsubscribe();
          delete activeListeners.leaderboard.main;
        }
      };
    }
  };

  // Dinlemeyi durdurmak için fonksiyon döndür
  return () => {
    if (activeListeners.leaderboard.main) {
      activeListeners.leaderboard.main.callbacks =
        activeListeners.leaderboard.main.callbacks.filter(c => c !== callback);

      // Callback kalmadıysa dinlemeyi durdur
      if (activeListeners.leaderboard.main.callbacks.length === 0) {
        activeListeners.leaderboard.main.unsubscribe();
        delete activeListeners.leaderboard.main;
      }
    }
  };
};

/**
 * Gönderi yorum sayısını dinler ve güncellemeleri takip eder
 * @param {string} postId - Dinlenecek gönderinin ID'si
 * @param {function} callback - Yorum sayısı değiştiğinde çağrılacak fonksiyon
 * @returns {function} - Dinlemeyi durdurmak için çağrılacak fonksiyon
 */
export const listenToPostComments = (postId, callback) => {
  if (!postId) return () => {};

  // Zaten dinleniyor mu kontrol et
  if (activeListeners.posts[postId]) {
    return activeListeners.posts[postId].addCallback(callback);
  }

  // Yeni dinleyici oluştur
  const commentsRef = collection(db, 'posts', postId, 'comments');
  const unsubscribe = onSnapshot(commentsRef, (snapshot) => {
    const commentCount = snapshot.size;

    // Gönderi dokümanını güncelle
    const postRef = doc(db, 'posts', postId);
    updateDoc(postRef, {
      commentsCount: commentCount
    }).catch(() => {
      // Sessizce hata yönetimi
    });

    // Tüm callback'leri çağır
    if (activeListeners.posts[postId]) {
      activeListeners.posts[postId].callbacks.forEach(cb => cb(commentCount));
    }
  }, () => {
    // Sessizce hata yönetimi
  });

  // Dinleyiciyi kaydet
  activeListeners.posts[postId] = {
    unsubscribe,
    callbacks: [callback],
    addCallback: (cb) => {
      if (!activeListeners.posts[postId].callbacks.includes(cb)) {
        activeListeners.posts[postId].callbacks.push(cb);
      }
      return () => {
        activeListeners.posts[postId].callbacks =
          activeListeners.posts[postId].callbacks.filter(c => c !== cb);

        // Callback kalmadıysa dinlemeyi durdur
        if (activeListeners.posts[postId].callbacks.length === 0) {
          activeListeners.posts[postId].unsubscribe();
          delete activeListeners.posts[postId];
        }
      };
    }
  };

  // Dinlemeyi durdurmak için fonksiyon döndür
  return () => {
    if (activeListeners.posts[postId]) {
      activeListeners.posts[postId].callbacks =
        activeListeners.posts[postId].callbacks.filter(cb => cb !== callback);

      // Callback kalmadıysa dinlemeyi durdur
      if (activeListeners.posts[postId].callbacks.length === 0) {
        activeListeners.posts[postId].unsubscribe();
        delete activeListeners.posts[postId];
      }
    }
  };
};

/**
 * Kullanıcı ünvanını belirler
 * @param {number} popularityScore - Popülerlik puanı
 * @returns {string} Kullanıcı ünvanı
 */
export const determineRank = (popularityScore) => {
  if (popularityScore >= 1000) return 'Efsane';
  if (popularityScore >= 500) return 'Yıldız';
  if (popularityScore >= 200) return 'Popüler';
  if (popularityScore >= 100) return 'Yükselen';
  if (popularityScore >= 50) return 'Aktif';
  if (popularityScore >= 20) return 'Başlangıç';
  return 'Yeni Üye';
};
