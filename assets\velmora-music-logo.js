import React from 'react';
import { View } from 'react-native';
import Svg, { Path, Circle, G, LinearGradient, Stop, Defs, Text, Ellipse, Rect } from 'react-native-svg';

const VelmoraMusicLogo = ({ width = 200, height = 200 }) => {
  return (
    <View style={{ width, height }}>
      <Svg width={width} height={height} viewBox="0 0 200 200">
        <Defs>
          <LinearGradient id="gradMain" x1="0%" y1="0%" x2="100%" y2="100%">
            <Stop offset="0%" stopColor="#1E3A8A" stopOpacity="1" />
            <Stop offset="100%" stopColor="#3B82F6" stopOpacity="1" />
          </LinearGradient>
          <LinearGradient id="gradGlow" x1="0%" y1="0%" x2="100%" y2="100%">
            <Stop offset="0%" stopColor="#1E3A8A" stopOpacity="0.7" />
            <Stop offset="100%" stopColor="#3B82F6" stopOpacity="0.7" />
          </LinearGradient>
          <LinearGradient id="gradAccent" x1="0%" y1="0%" x2="100%" y2="100%">
            <Stop offset="0%" stopColor="#F59E0B" stopOpacity="1" />
            <Stop offset="100%" stopColor="#F97316" stopOpacity="1" />
          </LinearGradient>
          <LinearGradient id="gradNote" x1="0%" y1="0%" x2="100%" y2="100%">
            <Stop offset="0%" stopColor="#F59E0B" stopOpacity="1" />
            <Stop offset="100%" stopColor="#F97316" stopOpacity="1" />
          </LinearGradient>
        </Defs>
        
        {/* Background Circle with Glow */}
        <Circle cx="100" cy="100" r="95" fill="rgba(30, 58, 138, 0.05)" />
        <Circle cx="100" cy="100" r="90" fill="none" stroke="url(#gradGlow)" strokeWidth="1" />
        
        {/* Main Circle */}
        <Circle cx="100" cy="100" r="80" fill="none" stroke="url(#gradMain)" strokeWidth="3" />
        
        {/* Decorative Elements */}
        <Circle cx="100" cy="100" r="70" fill="none" stroke="url(#gradMain)" strokeWidth="1" opacity="0.6" />
        <Circle cx="100" cy="100" r="60" fill="none" stroke="url(#gradMain)" strokeWidth="1" opacity="0.4" />
        
        {/* Music Staff Lines */}
        <Path
          d="M40,85 L160,85 M40,95 L160,95 M40,105 L160,105 M40,115 L160,115 M40,125 L160,125"
          stroke="url(#gradMain)"
          strokeWidth="1"
          opacity="0.3"
        />
        
        {/* V Letter - More Modern and Bold */}
        <Path
          d="M65,60 L100,140 L135,60"
          fill="none"
          stroke="url(#gradMain)"
          strokeWidth="10"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        
        {/* Music Notes */}
        <G transform="translate(135, 100) scale(0.9)">
          <Path
            d="M0,0 C6,0 12,-6 12,-18 C12,-30 6,-36 0,-36 C-6,-36 -12,-30 -12,-18 C-12,-6 -6,0 0,0 Z"
            fill="url(#gradNote)"
          />
          <Path
            d="M0,-36 L0,-90"
            stroke="url(#gradNote)"
            strokeWidth="5"
            strokeLinecap="round"
          />
        </G>
        
        <G transform="translate(65, 110) scale(0.7)">
          <Path
            d="M0,0 C5,0 10,-5 10,-15 C10,-25 5,-30 0,-30 C-5,-30 -10,-25 -10,-15 C-10,-5 -5,0 0,0 Z"
            fill="url(#gradNote)"
          />
          <Path
            d="M0,-30 L0,-70"
            stroke="url(#gradNote)"
            strokeWidth="4"
            strokeLinecap="round"
          />
        </G>
        
        {/* Musical Symbols */}
        <G transform="translate(100, 75) scale(0.8)">
          <Path
            d="M-15,0 C-15,-8.284 -8.284,-15 0,-15 C8.284,-15 15,-8.284 15,0 C15,8.284 8.284,15 0,15 C-8.284,15 -15,8.284 -15,0 Z"
            fill="none"
            stroke="url(#gradAccent)"
            strokeWidth="2"
          />
        </G>
        
        {/* Sparkles and Stars */}
        <Circle cx="50" cy="50" r="2" fill="#F59E0B" />
        <Circle cx="150" cy="50" r="2" fill="#F59E0B" />
        <Circle cx="50" cy="150" r="2" fill="#F59E0B" />
        <Circle cx="150" cy="150" r="2" fill="#F59E0B" />
        
        {/* Star Bursts */}
        <G transform="translate(30, 70)">
          <Path d="M0,0 L5,5 M0,5 L5,0 M2.5,-3 L2.5,8" stroke="#F59E0B" strokeWidth="1.5" />
        </G>
        <G transform="translate(170, 70)">
          <Path d="M0,0 L5,5 M0,5 L5,0 M2.5,-3 L2.5,8" stroke="#F59E0B" strokeWidth="1.5" />
        </G>
        <G transform="translate(100, 25)">
          <Path d="M0,0 L5,5 M0,5 L5,0 M2.5,-3 L2.5,8" stroke="#F59E0B" strokeWidth="1.5" />
        </G>
        
        {/* Sound Waves */}
        <Path
          d="M30,100 Q40,90 50,100 Q60,110 70,100 Q80,90 90,100"
          fill="none"
          stroke="url(#gradAccent)"
          strokeWidth="1.5"
          opacity="0.7"
        />
        <Path
          d="M110,100 Q120,90 130,100 Q140,110 150,100 Q160,90 170,100"
          fill="none"
          stroke="url(#gradAccent)"
          strokeWidth="1.5"
          opacity="0.7"
        />
        
        {/* Subtle Glow Effect */}
        <Circle cx="100" cy="100" r="100" fill="url(#gradMain)" opacity="0.05" />
      </Svg>
    </View>
  );
};

export default VelmoraMusicLogo;
