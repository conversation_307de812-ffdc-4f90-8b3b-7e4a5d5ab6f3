import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  TextInput,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { doc, getDoc, setDoc } from 'firebase/firestore';
import { db, auth } from '../firebase';
import { useToast } from '../contexts/ToastContext';

const RankSettingsScreen = () => {
  const navigation = useNavigation();
  const { showToast } = useToast();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [isAdmin, setIsAdmin] = useState(false);
  const [rankThresholds, setRankThresholds] = useState({
    'Efsane': 1000,
    'Yıldız': 500,
    'Popüler': 200,
    'Yükselen': 100,
    'Aktif': 50,
    '<PERSON><PERSON><PERSON><PERSON><PERSON>': 20,
    'Yeni Üye': 0
  });

  useEffect(() => {
    const checkAdminStatus = async () => {
      try {
        if (!auth.currentUser) {
          setLoading(false);
          return;
        }

        const userRef = doc(db, 'users', auth.currentUser.uid);
        const userSnap = await getDoc(userRef);
        
        if (userSnap.exists() && userSnap.data().isAdmin) {
          setIsAdmin(true);
          await loadRankSettings();
        } else {
          showToast({
            message: 'Bu sayfaya erişim yetkiniz bulunmamaktadır.',
            type: 'error'
          });
          navigation.goBack();
        }
      } catch (error) {
        console.error('Admin kontrolü hatası:', error);
        showToast({
          message: 'Bir hata oluştu. Lütfen tekrar deneyin.',
          type: 'error'
        });
      } finally {
        setLoading(false);
      }
    };

    checkAdminStatus();
  }, []);

  const loadRankSettings = async () => {
    try {
      const settingsRef = doc(db, 'settings', 'ranks');
      const settingsSnap = await getDoc(settingsRef);
      
      if (settingsSnap.exists()) {
        setRankThresholds(settingsSnap.data().thresholds);
      }
    } catch (error) {
      console.error('Ayarları yükleme hatası:', error);
      showToast({
        message: 'Ayarlar yüklenirken bir hata oluştu.',
        type: 'error'
      });
    }
  };

  const handleSaveSettings = async () => {
    try {
      setSaving(true);
      
      // Değerlerin sayı olduğundan ve doğru sıralandığından emin ol
      const thresholds = {
        'Efsane': parseInt(rankThresholds['Efsane']),
        'Yıldız': parseInt(rankThresholds['Yıldız']),
        'Popüler': parseInt(rankThresholds['Popüler']),
        'Yükselen': parseInt(rankThresholds['Yükselen']),
        'Aktif': parseInt(rankThresholds['Aktif']),
        'Başlangıç': parseInt(rankThresholds['Başlangıç']),
        'Yeni Üye': 0
      };
      
      // Değerlerin sıralı olduğunu kontrol et
      if (
        thresholds['Efsane'] <= thresholds['Yıldız'] ||
        thresholds['Yıldız'] <= thresholds['Popüler'] ||
        thresholds['Popüler'] <= thresholds['Yükselen'] ||
        thresholds['Yükselen'] <= thresholds['Aktif'] ||
        thresholds['Aktif'] <= thresholds['Başlangıç'] ||
        thresholds['Başlangıç'] < thresholds['Yeni Üye']
      ) {
        Alert.alert(
          'Geçersiz Değerler',
          'Lütfen değerlerin büyükten küçüğe doğru sıralı olduğundan emin olun.'
        );
        setSaving(false);
        return;
      }
      
      await setDoc(doc(db, 'settings', 'ranks'), {
        thresholds,
        updatedAt: new Date(),
        updatedBy: auth.currentUser.uid
      });
      
      showToast({
        message: 'Ünvan ayarları başarıyla kaydedildi.',
        type: 'success'
      });
      
      navigation.goBack();
    } catch (error) {
      console.error('Ayarları kaydetme hatası:', error);
      showToast({
        message: 'Ayarlar kaydedilirken bir hata oluştu.',
        type: 'error'
      });
    } finally {
      setSaving(false);
    }
  };

  const handleInputChange = (rank, value) => {
    // Sadece sayısal değerlere izin ver
    if (value === '' || /^\d+$/.test(value)) {
      setRankThresholds(prev => ({
        ...prev,
        [rank]: value
      }));
    }
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#3498db" />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Ünvan Ayarları</Text>
      </View>

      <ScrollView style={styles.content}>
        <Text style={styles.description}>
          Bu sayfada kullanıcı ünvanlarının popülerlik eşik değerlerini ayarlayabilirsiniz.
          Popülerlik puanı, beğeni, yorum ve takipçi sayısına göre hesaplanır.
        </Text>
        
        <View style={styles.infoBox}>
          <Text style={styles.infoTitle}>Popülerlik Hesaplama Formülü:</Text>
          <Text style={styles.infoText}>• 10 beğeni = 1 popülerlik puanı</Text>
          <Text style={styles.infoText}>• 3 yorum = 1 popülerlik puanı</Text>
          <Text style={styles.infoText}>• 1 takipçi = 1 popülerlik puanı</Text>
        </View>

        <View style={styles.rankContainer}>
          <Text style={styles.sectionTitle}>Ünvan Eşik Değerleri</Text>
          
          {Object.entries(rankThresholds)
            .sort(([, a], [, b]) => b - a) // Değerlere göre büyükten küçüğe sırala
            .map(([rank, threshold]) => (
              <View key={rank} style={styles.rankItem}>
                <View style={styles.rankBadgeContainer}>
                  <Text style={[
                    styles.rankBadge,
                    rank === 'Efsane' && styles.legendaryBadge,
                    rank === 'Yıldız' && styles.starBadge,
                    rank === 'Popüler' && styles.popularBadge,
                    rank === 'Yükselen' && styles.risingBadge,
                    rank === 'Aktif' && styles.activeBadge,
                    rank === 'Başlangıç' && styles.beginnerBadge,
                    rank === 'Yeni Üye' && styles.newbieBadge,
                  ]}>{rank}</Text>
                </View>
                
                {rank === 'Yeni Üye' ? (
                  <Text style={styles.fixedThreshold}>0 (Sabit)</Text>
                ) : (
                  <TextInput
                    style={styles.thresholdInput}
                    value={threshold.toString()}
                    onChangeText={(value) => handleInputChange(rank, value)}
                    keyboardType="numeric"
                    placeholder="Eşik değeri"
                    placeholderTextColor="#999"
                  />
                )}
              </View>
            ))}
        </View>
      </ScrollView>

      <View style={styles.footer}>
        <TouchableOpacity
          style={styles.saveButton}
          onPress={handleSaveSettings}
          disabled={saving}
        >
          {saving ? (
            <ActivityIndicator size="small" color="#fff" />
          ) : (
            <Text style={styles.saveButtonText}>Ayarları Kaydet</Text>
          )}
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#000',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: 40,
    paddingBottom: 10,
    paddingHorizontal: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#333',
  },
  backButton: {
    padding: 5,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
    marginLeft: 15,
  },
  content: {
    flex: 1,
    padding: 15,
  },
  description: {
    fontSize: 16,
    color: '#ccc',
    marginBottom: 20,
    lineHeight: 22,
  },
  infoBox: {
    backgroundColor: 'rgba(52, 152, 219, 0.1)',
    borderRadius: 10,
    padding: 15,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: 'rgba(52, 152, 219, 0.3)',
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#3498db',
    marginBottom: 10,
  },
  infoText: {
    fontSize: 14,
    color: '#ccc',
    marginBottom: 5,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 15,
  },
  rankContainer: {
    marginBottom: 20,
  },
  rankItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#333',
  },
  rankBadgeContainer: {
    flex: 1,
  },
  rankBadge: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#000',
    backgroundColor: '#ccc',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 15,
    overflow: 'hidden',
    alignSelf: 'flex-start',
  },
  legendaryBadge: {
    backgroundColor: '#FFD700', // Altın
  },
  starBadge: {
    backgroundColor: '#E6E6FA', // Lavanta
  },
  popularBadge: {
    backgroundColor: '#FF6B6B', // Kırmızı
  },
  risingBadge: {
    backgroundColor: '#4A90E2', // Mavi
  },
  activeBadge: {
    backgroundColor: '#50C878', // Yeşil
  },
  beginnerBadge: {
    backgroundColor: '#9370DB', // Mor
  },
  newbieBadge: {
    backgroundColor: '#A9A9A9', // Gri
  },
  thresholdInput: {
    backgroundColor: '#333',
    color: '#fff',
    borderRadius: 5,
    paddingHorizontal: 10,
    paddingVertical: 8,
    width: 100,
    textAlign: 'center',
    fontSize: 16,
  },
  fixedThreshold: {
    color: '#999',
    width: 100,
    textAlign: 'center',
    fontSize: 16,
  },
  footer: {
    padding: 15,
    borderTopWidth: 1,
    borderTopColor: '#333',
  },
  saveButton: {
    backgroundColor: '#3498db',
    paddingVertical: 12,
    borderRadius: 5,
    alignItems: 'center',
  },
  saveButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default RankSettingsScreen;
