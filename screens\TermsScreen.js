// screens/TermsScreen.js
import React from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';

const TermsScreen = () => {
  const navigation = useNavigation();

  return (
    <View style={styles.container}>
      {/* Üst Başlık */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>VELMORA Kullanım Koşulları</Text>
      </View>

      <ScrollView style={styles.content} contentContainerStyle={styles.contentContainer}>
        <Text style={styles.paragraph}>
          Bu sözleşme, VELMORA uygulamasını kullanan tüm kullanıcılar için geçerlidir. Uygulamayı kullanmaya başlamadan önce bu sözleşmeyi dikkatlice okuyunuz.
        </Text>

        <Text style={styles.subHeader}>1. Genel Hükümler</Text>
        <Text style={styles.paragraph}>
          VELMORA, kullanıcıların metin içerikleri paylaşabildiği ve diğer kullanıcılarla etkileşimde bulunabildiği bir sosyal medya platformudur. Uygulamayı kullanarak, bu sözleşmenin tüm şartlarını kabul etmiş sayılırsınız.
        </Text>

        <Text style={styles.subHeader}>2. Kullanıcı Hesapları</Text>
        <Text style={styles.paragraph}>
          - Kullanıcılar, kayıt sırasında doğru ve güncel bilgiler vermekle yükümlüdür.{"\n"}
          - Kullanıcılar, hesap bilgilerinin güvenliğini sağlamakla yükümlüdürler. Hesap bilgilerinin üçüncü şahıslar tarafından kullanılmasından doğan zararlardan VELMORA sorumlu tutulamaz.{"\n"}
          - Kullanıcılar, hesaplarını üçüncü şahıslara devredemez, satamaz veya kiralayamaz.{"\n"}
          - Kullanıcılar, 18 yaşından küçük olmaları halinde velilerinden veya yasal vasilerinden izin almak zorundadırlar.
        </Text>

        <Text style={styles.subHeader}>3. İçerik Politikası</Text>
        <Text style={styles.paragraph}>
          - Kullanıcılar, aşağıdaki tür içerikleri paylaşamaz:{"\n"}
          • Şiddet, nefret söylemi, ırkçılık, cinsiyetçilik, ayrımcılık içeren içerikler{"\n"}
          • Telif hakkıyla korunan içerikler (izinsiz paylaşım){"\n"}
          • Pornografik veya yetişkinlere özel içerikler{"\n"}
          • Kişisel verileri ihlal eden veya gizlilik haklarını ihlal eden içerikler{"\n"}
          • Yasa dışı faaliyetleri teşvik eden içerikler{"\n"}
          • Spam içerikler, yanıltıcı reklamlar ve sahte bilgiler{"\n"}
          - Bu tür içerikler bildirildiği takdirde VELMORA tarafından kaldırılır ve hesabınız askıya alınabilir veya kapatılabilir.
        </Text>

        <Text style={styles.subHeader}>4. Gizlilik</Text>
        <Text style={styles.paragraph}>
          - Kullanıcıların kişisel verileri Gizlilik Politikamız kapsamında korunmaktadır. Bu bilgilere VELMORA tarafından erişim sadece uygulamanın işleyişi için gereklidir ve üçüncü kişilerle paylaşılmaz.{"\n"}
          - Kullanıcılar, kişisel bilgilerinin uygulama içindeki paylaşımlarında dikkatli olmalıdır. Kullanıcıların kendi paylaşımlarından kaynaklanan zararlardan VELMORA sorumlu tutulamaz.
        </Text>

        <Text style={styles.subHeader}>5. Telif Hakları ve Fikri Mülkiyet</Text>
        <Text style={styles.paragraph}>
          - Kullanıcılar, paylaştıkları içeriklerin telif hakkına ve fikri mülkiyet haklarına sahip olmalı veya bunların kullanım hakkına sahip olmalıdır.{"\n"}
          - Kullanıcılar, paylaştıkları içeriklerin VELMORA tarafından uygulama içerisinde kullanımı, çoğaltılması, dağıtılması, tanıtımı ve reklamı için dünya çapında, ücretsiz ve süresiz bir lisans verdiğini kabul eder.
        </Text>

        <Text style={styles.subHeader}>6. Sorumluluk Reddi</Text>
        <Text style={styles.paragraph}>
          - VELMORA, kullanıcıların paylaştığı içeriklerden sorumlu değildir. İçeriklerin doğruluğunu veya kalitesini garanti etmez.{"\n"}
          - Uygulamanın kesintisiz veya hatasız çalışacağı garanti edilmez. Teknik sorunlar, bakım veya güncelleme nedeniyle uygulamada kesintiler olabilir.
        </Text>

        <Text style={styles.subHeader}>7. Uygulama Kullanım Kuralları</Text>
        <Text style={styles.paragraph}>
          - Uygulamayı hukuka aykırı faaliyetler için kullanmak yasaktır.{"\n"}
          - Diğer kullanıcıları taciz etmek, tehdit etmek, rahatsız etmek, hedef göstermek yasaktır.{"\n"}
          - Spam içerikler, sahte hesap oluşturma veya otomatik bot kullanımı yasaktır.{"\n"}
          - Uygulamanın güvenliğini tehlikeye atacak faaliyetlerde bulunmak (hacklemek, sızmak, verileri çalmak) yasaktır.
        </Text>

        <Text style={styles.subHeader}>8. Hesap İptali ve Kapatma</Text>
        <Text style={styles.paragraph}>
          - Kullanıcılar istedikleri zaman hesaplarını kapatabilirler. Hesap kapatma işlemi geri alınamaz ve tüm kullanıcı verileri kalıcı olarak silinir.{"\n"}
          - VELMORA, sözleşme kurallarına aykırılık halinde kullanıcı hesaplarını önceden uyarı yapmaksızın geçici veya kalıcı olarak kapatma hakkını saklı tutar.
        </Text>

        <Text style={styles.subHeader}>9. Şikayet ve Bildirimler</Text>
        <Text style={styles.paragraph}>
          - Kullanıcılar, uygunsuz buldukları içerikleri veya davranışları VELMORA'ya rapor edebilir.{"\n"}
          - Yapılan şikayetler gizlilik içerisinde değerlendirilir ve gerekli görülürse işlem yapılır.
        </Text>

        <Text style={styles.subHeader}>10. Değişiklik ve Güncellemeler</Text>
        <Text style={styles.paragraph}>
          VELMORA, bu sözleşmeyi gerekli gördüğünde değiştirme hakkına sahiptir. Yapılan değişiklikler kullanıcılarla uygulama üzerinden paylaşılacaktır. Kullanıcılar, değişiklikler sonrası uygulamayı kullanmaya devam ettikleri takdirde yeni sözleşmeyi kabul etmiş sayılırlar.
        </Text>

        <Text style={styles.subHeader}>11. Yasal Hükümler ve Uyuşmazlıkların Çözümü</Text>
        <Text style={styles.paragraph}>
          - Bu sözleşme Türkiye Cumhuriyeti kanunlarına tabidir.{"\n"}
          - Taraflar arasında oluşabilecek tüm uyuşmazlıklarda İstanbul Mahkemeleri ve İcra Daireleri yetkilidir.
        </Text>

        <Text style={styles.subHeader}>12. Yürürlük</Text>
        <Text style={styles.paragraph}>
          Bu sözleşme, kullanıcıların uygulamayı ilk kullandıkları andan itibaren yürürlüğe girer ve kullanıcı hesabı kapatılana kadar geçerlidir.
        </Text>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#000' },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    height: 60,
    borderBottomWidth: 1,
    borderBottomColor: '#333',
    paddingHorizontal: 10,
    backgroundColor: '#000',
  },
  backButton: { padding: 5 },
  headerTitle: { flex: 1, textAlign: 'center', color: '#fff', fontSize: 20, fontWeight: 'bold' },
  content: { paddingHorizontal: 20 },
  contentContainer: { paddingVertical: 20 },
  paragraph: { color: '#fff', fontSize: 16, lineHeight: 24, marginBottom: 15 },
  subHeader: { color: '#fff', fontSize: 18, fontWeight: 'bold', marginTop: 20, marginBottom: 10 },
});

export default TermsScreen;
