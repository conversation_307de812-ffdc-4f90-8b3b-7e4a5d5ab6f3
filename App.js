// App.js
import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ActivityIndicator,
  ScrollView,
  Animated,
  KeyboardAvoidingView,
  Platform,
  ToastAndroid,
  Alert,
} from 'react-native';
import {
  registerUser,
  loginUser,
  resetPassword,
  auth,
  getUserProfile,
} from './firebase';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { onAuthStateChanged, signOut } from 'firebase/auth';

import { NavigationContainer } from '@react-navigation/native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createStackNavigator } from '@react-navigation/stack';
import { Ionicons, FontAwesome } from '@expo/vector-icons';
import { ThemeProvider } from './contexts/ThemeContext';
import { LanguageProvider } from './contexts/LanguageContext';
import { ToastProvider } from './contexts/ToastContext';
import { LinearGradient } from 'expo-linear-gradient';

// Ekran bileşenleri
import FeedScreen from './components/FeedScreen';
import SearchScreen from './screens/SearchScreen';
import SettingsScreen from './screens/SettingsScreen';
import Hesap from './screens/Hesap';
import ProfileEditScreen from './screens/ProfileEditScreen';
import AddPostScreen from './screens/AddPostScreen';
import CommentsScreen from './screens/CommentsScreen';
import OtherProfileScreen from './screens/OtherProfileScreen';
import FollowListScreen from './screens/FollowListScreen';
import LikersScreen from './screens/LikersScreen';

// Yeni eklenen ekranlar
import TermsScreen from './screens/TermsScreen';
import PrivacyPolicyScreen from './screens/PrivacyPolicyScreen';
import BlockedUsersScreen from './screens/BlockedUsersScreen';
import SavedPostsScreen from './screens/SavedPostsScreen';
import LikedPostsScreen from './screens/LikedPostsScreen';
import CommentedPostsScreen from './screens/CommentedPostsScreen';
import NotificationsScreen from './screens/NotificationsScreen';
import RankSettingsScreen from './screens/RankSettingsScreen';
import PopularityInfoScreen from './screens/PopularityInfoScreen';
// LeaderboardScreen artık Tab Navigator'da değil, SearchScreen içinde kullanılıyor

import { likePost, addComment } from './firebaseActions';
import { listenToLeaderboard, updateUserPopularity } from './utils/popularityUtils';

const Tab = createBottomTabNavigator();
const Stack = createStackNavigator();

const MainTabs = ({ user }) => (
  <Tab.Navigator
    screenOptions={{
      headerShown: false,
      tabBarStyle: { backgroundColor: '#000' },
      tabBarActiveTintColor: '#004080',
      tabBarInactiveTintColor: '#aaa',
    }}
  >
    <Tab.Screen
      name="Ana Sayfa"
      children={() => <FeedScreen user={user} onLike={likePost} onComment={addComment} />}
      options={{
        tabBarIcon: ({ color, size }) => <Ionicons name="home" size={size} color={color} />,
      }}
    />
    <Tab.Screen
      name="Ara"
      component={SearchScreen}
      options={{
        tabBarIcon: ({ color, size }) => <Ionicons name="search" size={size} color={color} />,
      }}
    />
    <Tab.Screen
      name="Gönderi Ekle"
      component={AddPostScreen}
      options={{
        tabBarIcon: ({ color, size }) => <Ionicons name="add-circle" size={size} color={color} />,
      }}
    />

    <Tab.Screen
      name="Profil"
      children={() => <Hesap user={user} />}
      options={{
        tabBarIcon: ({ color, size }) => <Ionicons name="person" size={size} color={color} />,
      }}
    />
    <Tab.Screen
      name="Ayarlar"
      children={() => <SettingsScreen user={user} />}
      options={{
        tabBarIcon: ({ color, size }) => <Ionicons name="settings" size={size} color={color} />,
      }}
    />
  </Tab.Navigator>
);

const App = () => {
  // State değişkenleri
  const [isLogin, setIsLogin] = useState(true);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [username, setUsername] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(true);
  const [loggingIn, setLoggingIn] = useState(false);
  const [user, setUser] = useState(null);
  const [rememberMe, setRememberMe] = useState(false);
  const [passwordVisible, setPasswordVisible] = useState(false);
  const [passwordStrength, setPasswordStrength] = useState(0); // 0: yok, 1: zayıf, 2: orta, 3: güçlü

  // Animasyon değişkenleri
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.9)).current;
  // Animasyonları başlatmak için hazır ol

  // Kullanıcı durumunu kontrol et ve verileri önceden yükle
  useEffect(() => {
    // Uygulama açılır açılmaz tüm verileri yüklemeye başla
    const preloadData = async () => {
      try {
        // Liderlik tablosunu önceden yükle
        const unsubscribeLeaderboard = listenToLeaderboard(() => {
          console.log('Liderlik tablosu verileri yüklendi');
        });

        // Kullanıcı durumunu kontrol et
        if (auth.currentUser) {
          if (!auth.currentUser.emailVerified) {
            setUser(null);
          } else {
            // Kullanıcı profilini yükle
            const profileData = await getUserProfile(auth.currentUser.uid);
            setUser({ uid: auth.currentUser.uid, ...profileData });

            // Kullanıcının popülerlik değerini güncelle
            updateUserPopularity(auth.currentUser.uid, true).catch(err =>
              console.error('Popülerlik güncelleme hatası:', err)
            );
          }
        }

        // Yükleme tamamlandı
        setLoading(false);

        // Component unmount olduğunda dinleyicileri temizle
        return () => {
          unsubscribeLeaderboard();
        };
      } catch (err) {
        console.error('Veri ön yükleme hatası:', err);
        setLoading(false);
      }
    };

    // Veri ön yüklemeyi başlat
    preloadData();
  }, []);

  // Animasyonları başlat
  useEffect(() => {
    if (!user) {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        }),
        Animated.spring(scaleAnim, {
          toValue: 1,
          friction: 8,
          tension: 40,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [fadeAnim, scaleAnim, user]);

  // Beni hatırla durumunu kontrol et
  useEffect(() => {
    const checkRememberMe = async () => {
      try {
        const savedEmail = await AsyncStorage.getItem('rememberedEmail');
        const savedPassword = await AsyncStorage.getItem('rememberedPassword');

        if (savedEmail && savedPassword) {
          setEmail(savedEmail);
          setPassword(savedPassword);
          setRememberMe(true);
        }
      } catch (error) {
        console.error('Hatırlanan giriş bilgilerini alma hatası:', error);
      }
    };

    checkRememberMe();
  }, []);

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (currentUser) => {
      if (currentUser) {
        if (!currentUser.emailVerified) {
          ToastAndroid.show('Lütfen e-posta adresinizi doğrulayın.', ToastAndroid.LONG);
          await signOut(auth);
          setUser(null);
          setLoading(false);
        } else {
          const profileData = await getUserProfile(currentUser.uid);
          setUser({ uid: currentUser.uid, ...profileData });
          setLoading(false);
        }
      } else {
        setUser(null);
        setLoading(false);
      }
    });
    return () => unsubscribe();
  }, []);

  // Şifre güvenliğini kontrol eden fonksiyon
  const checkPasswordStrength = (pass) => {
    if (!pass) {
      setPasswordStrength(0);
      return;
    }

    let strength = 0;

    // Uzunluk kontrolü
    if (pass.length >= 8) strength += 1;

    // Karmaşıklık kontrolleri
    if (/[A-Z]/.test(pass)) strength += 1; // Büyük harf
    if (/[0-9]/.test(pass)) strength += 1; // Rakam
    if (/[^A-Za-z0-9]/.test(pass)) strength += 1; // Özel karakter

    setPasswordStrength(Math.min(3, strength));
  };

  const validateInputs = () => {
    if (!email.trim()) {
      setError('Lütfen e-posta adresinizi giriniz.');
      return false;
    }
    const emailRegex = /\S+@\S+\.\S+/;
    if (!emailRegex.test(email)) {
      setError('Geçersiz e-posta formatı. Lütfen geçerli bir e-posta adresi girin.');
      return false;
    }
    if (password.length < 8) {
      setError('Şifreniz en az 8 karakter olmalıdır.');
      return false;
    }
    if (!isLogin && username.trim() === '') {
      setError('Lütfen kullanıcı adınızı giriniz.');
      return false;
    }
    setError('');
    return true;
  };

  const handleLogin = async () => {
    if (!validateInputs()) return;
    setLoggingIn(true);
    setError('');

    try {
      const userCredential = await loginUser(email, password);
      if (userCredential.user) {
        if (!userCredential.user.emailVerified) {
          ToastAndroid.show('Lütfen e-posta adresinizi doğrulayın.', ToastAndroid.LONG);
          await signOut(auth);
        } else {
          // Beni hatırla seçeneği işaretliyse bilgileri kaydet
          if (rememberMe) {
            try {
              await AsyncStorage.setItem('rememberedEmail', email);
              await AsyncStorage.setItem('rememberedPassword', password);
            } catch (error) {
              console.error('Giriş bilgilerini kaydetme hatası:', error);
            }
          } else {
            // Beni hatırla seçeneği işaretli değilse kayıtlı bilgileri temizle
            try {
              await AsyncStorage.removeItem('rememberedEmail');
              await AsyncStorage.removeItem('rememberedPassword');
            } catch (error) {
              console.error('Giriş bilgilerini silme hatası:', error);
            }
          }

          const profileData = await getUserProfile(userCredential.user.uid);
          setUser({ uid: userCredential.user.uid, ...profileData });
        }
      }
    } catch (err) {
      let message = '';
      switch (err.code) {
        case 'auth/user-not-found':
          message = 'Bu e-posta adresine ait bir hesap bulunamadı. Lütfen kayıt olun veya farklı bir e-posta adresi deneyin.';
          break;
        case 'auth/wrong-password':
          message = 'Yanlış şifre girdiniz. Şifrenizi hatırlamıyorsanız "Şifremi Unuttum" seçeneğini kullanabilirsiniz.';
          break;
        case 'auth/invalid-email':
          message = 'Geçersiz e-posta formatı. Lütfen geçerli bir e-posta adresi girin.';
          break;
        case 'auth/too-many-requests':
          message = 'Çok fazla başarısız giriş denemesi. Lütfen daha sonra tekrar deneyin veya şifrenizi sıfırlayın.';
          break;
        case 'auth/user-disabled':
          message = 'Bu hesap devre dışı bırakılmış. Lütfen destek ekibiyle iletişime geçin.';
          break;
        default:
          message = 'Giriş başarısız: ' + err.message;
      }
      setError(message);
    } finally {
      setLoggingIn(false);
    }
  };

  const handleRegister = async () => {
    if (!validateInputs()) return;
    setLoggingIn(true);
    setError('');
    try {
      await registerUser(email, password, username);
      ToastAndroid.show('Kayıt işleminiz tamamlandı. Lütfen e-posta adresinizi doğrulayın ve ardından giriş yapın.', ToastAndroid.LONG);
      setIsLogin(true);
    } catch (err) {
      let message = '';
      switch (err.code) {
        case 'auth/email-already-in-use':
          message = 'Bu e-posta adresi zaten kullanılıyor. Lütfen giriş yapın veya farklı bir e-posta adresi kullanın.';
          break;
        case 'auth/weak-password':
          message = 'Zayıf şifre. Lütfen daha güçlü bir şifre seçin (en az 6 karakter, rakam ve harf içeren).';
          break;
        case 'auth/invalid-email':
          message = 'Geçersiz e-posta formatı. Lütfen geçerli bir e-posta adresi girin.';
          break;
        case 'auth/operation-not-allowed':
          message = 'Kayıt işlemi şu anda kullanılamıyor. Lütfen daha sonra tekrar deneyin.';
          break;
        case 'auth/network-request-failed':
          message = 'Ağ bağlantısı hatası. Lütfen internet bağlantınızı kontrol edin ve tekrar deneyin.';
          break;
        default:
          message = 'Kayıt başarısız: ' + err.message;
      }
      setError(message);
    } finally {
      setLoggingIn(false);
    }
  };

  const handleResetPassword = async () => {
    if (!email.trim()) {
      setError('Lütfen şifre sıfırlama için e-posta adresinizi giriniz.');
      return;
    }

    const emailRegex = /\S+@\S+\.\S+/;
    if (!emailRegex.test(email)) {
      setError('Geçersiz e-posta formatı. Lütfen geçerli bir e-posta adresi girin.');
      return;
    }

    setLoggingIn(true);
    setError('');
    try {
      await resetPassword(email);
      ToastAndroid.show('Şifre sıfırlama e-postası gönderildi. Lütfen e-posta kutunuzu kontrol edin.', ToastAndroid.LONG);
    } catch (err) {
      let message = '';
      switch (err.code) {
        case 'auth/user-not-found':
          message = 'Bu e-posta adresine ait bir hesap bulunamadı.';
          break;
        case 'auth/invalid-email':
          message = 'Geçersiz e-posta formatı. Lütfen geçerli bir e-posta adresi girin.';
          break;
        case 'auth/too-many-requests':
          message = 'Çok fazla istek gönderildi. Lütfen daha sonra tekrar deneyin.';
          break;
        default:
          message = 'Şifre sıfırlama başarısız: ' + err.message;
      }
      setError(message);
    } finally {
      setLoggingIn(false);
    }
  };

  if (loading) {
    return (
      <View style={[styles.container, styles.centerContent]}>
        <LinearGradient
          colors={['#000000', '#0F172A', '#1E293B']}
          style={styles.splashContainer}
        >
          <Animated.View
            style={{
              opacity: fadeAnim,
              transform: [{ scale: scaleAnim }]
            }}
          >
            <Text style={styles.splashTitle}>VELMORA</Text>
            <ActivityIndicator size="large" color="#3B82F6" style={{ marginTop: 20 }} />
          </Animated.View>
        </LinearGradient>
      </View>
    );
  }

  if (user) {
    return (
      <LanguageProvider>
        <ThemeProvider>
          <ToastProvider>
            <NavigationContainer>
              <Stack.Navigator screenOptions={{ headerShown: false }}>
                <Stack.Screen name="MainTabs">
                  {() => <MainTabs user={user} />}
                </Stack.Screen>
                <Stack.Screen name="ProfiliDüzenle" component={ProfileEditScreen} />
                <Stack.Screen name="Comments" component={CommentsScreen} />
                <Stack.Screen name="OtherProfile" component={OtherProfileScreen} />
                <Stack.Screen name="FollowListScreen" component={FollowListScreen} />
                <Stack.Screen name="LikersScreen" component={LikersScreen} />
                <Stack.Screen name="TermsScreen" component={TermsScreen} />
                <Stack.Screen name="PrivacyPolicyScreen" component={PrivacyPolicyScreen} />
                <Stack.Screen name="BlockedUsers" component={BlockedUsersScreen} />
                <Stack.Screen name="SavedPosts" component={SavedPostsScreen} />
                <Stack.Screen name="LikedPosts" component={LikedPostsScreen} />
                <Stack.Screen name="CommentedPosts" component={CommentedPostsScreen} />
                <Stack.Screen name="Notifications" component={NotificationsScreen} />
                <Stack.Screen name="RankSettings" component={RankSettingsScreen} />
                <Stack.Screen name="PopularityInfo" component={PopularityInfoScreen} />
              </Stack.Navigator>
            </NavigationContainer>
          </ToastProvider>
        </ThemeProvider>
      </LanguageProvider>
    );
  }

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={{ flex: 1 }}
    >
      <LinearGradient
        colors={['#000000', '#0F172A', '#1E293B']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={{ flex: 1 }}
      >
        <ScrollView contentContainerStyle={styles.container}>
          <Animated.View
            style={[styles.loginContainer, {
              opacity: fadeAnim,
              transform: [{ scale: scaleAnim }]
            }]}
          >
            <View style={styles.logoContainer}>
              <Text style={styles.appTitle}>VELMORA</Text>
            </View>

            {error ? (
              <View style={styles.errorContainer}>
                <FontAwesome name="exclamation-circle" size={18} color="#EF4444" style={{ marginRight: 8 }} />
                <Text style={styles.errorText}>{error}</Text>
              </View>
            ) : null}

            <View style={styles.formContainer}>
              <Text style={styles.inputLabel}>E-posta</Text>
              <View style={styles.inputContainer}>
                <FontAwesome name="envelope" size={18} color="#3B82F6" style={styles.inputIcon} />
                <TextInput
                  style={styles.input}
                  placeholder="E-posta adresinizi girin"
                  placeholderTextColor="#64748B"
                  value={email}
                  onChangeText={setEmail}
                  autoCapitalize="none"
                  keyboardType="email-address"
                />
              </View>
            </View>

            <View style={styles.formContainer}>
              <Text style={styles.inputLabel}>Şifre</Text>
              <View style={styles.inputContainer}>
                <FontAwesome name="lock" size={18} color="#3B82F6" style={styles.inputIcon} />
                <TextInput
                  style={styles.input}
                  placeholder="Şifrenizi girin (En az 8 karakter)"
                  placeholderTextColor="#64748B"
                  secureTextEntry={!passwordVisible}
                  value={password}
                  onChangeText={(text) => {
                    // Emoji ve özel karakterleri filtrele
                    const filteredText = text.replace(/[^\x00-\x7F]/g, '');
                    setPassword(filteredText);
                    checkPasswordStrength(filteredText);
                  }}
                />
                <TouchableOpacity
                  style={styles.visibilityIcon}
                  onPress={() => setPasswordVisible(!passwordVisible)}
                >
                  <FontAwesome
                    name={passwordVisible ? "eye-slash" : "eye"}
                    size={18}
                    color="#3B82F6"
                  />
                </TouchableOpacity>
              </View>
              {!isLogin && (
                <View style={styles.passwordStrengthContainer}>
                  <View style={styles.strengthBarContainer}>
                    <View
                      style={[styles.strengthBar,
                        { width: passwordStrength === 0 ? '5%' :
                                passwordStrength === 1 ? '33%' :
                                passwordStrength === 2 ? '66%' : '100%',
                          backgroundColor: passwordStrength === 0 ? '#374151' :
                                          passwordStrength === 1 ? '#EF4444' :
                                          passwordStrength === 2 ? '#F59E0B' : '#10B981'
                        }
                      ]}
                    />
                  </View>
                  <View style={styles.strengthLabelContainer}>
                    <Text style={[styles.strengthText,
                      { color: passwordStrength === 0 ? '#9CA3AF' :
                               passwordStrength === 1 ? '#EF4444' :
                               passwordStrength === 2 ? '#F59E0B' : '#10B981' }]}>
                      {passwordStrength === 0 ? 'Şifre güvenliği: Yok' :
                       passwordStrength === 1 ? 'Şifre güvenliği: Zayıf' :
                       passwordStrength === 2 ? 'Şifre güvenliği: Orta' :
                       'Şifre güvenliği: Güçlü'}
                    </Text>
                    <Ionicons
                      name={passwordStrength === 0 ? 'lock-open' :
                            passwordStrength === 1 ? 'alert-circle' :
                            passwordStrength === 2 ? 'shield-half' : 'shield-checkmark'}
                      size={16}
                      color={passwordStrength === 0 ? '#9CA3AF' :
                             passwordStrength === 1 ? '#EF4444' :
                             passwordStrength === 2 ? '#F59E0B' : '#10B981'}
                      style={{ marginLeft: 5 }}
                    />
                  </View>
                </View>
              )}
            </View>

            {!isLogin && (
              <View style={styles.formContainer}>
                <Text style={styles.inputLabel}>Kullanıcı Adı</Text>
                <View style={styles.inputContainer}>
                  <FontAwesome name="user" size={18} color="#3B82F6" style={styles.inputIcon} />
                  <TextInput
                    style={styles.input}
                    placeholder="Kullanıcı adınızı girin"
                    placeholderTextColor="#64748B"
                    value={username}
                    onChangeText={setUsername}
                  />
                </View>
              </View>
            )}

            <View style={styles.rememberContainer}>
              <TouchableOpacity
                style={styles.rememberMeButton}
                onPress={() => setRememberMe(!rememberMe)}
              >
                <View style={[styles.checkbox, rememberMe && styles.checkboxChecked]}>
                  {rememberMe && <FontAwesome name="check" size={12} color="#fff" />}
                </View>
                <Text style={styles.rememberText}>Beni Hatırla</Text>
              </TouchableOpacity>

              {isLogin && (
                <TouchableOpacity
                  style={styles.forgotButton}
                  onPress={handleResetPassword}
                  activeOpacity={0.7}
                >
                    <Text style={styles.forgotText}>Şifremi Unuttum</Text>
                </TouchableOpacity>
              )}
            </View>

            <TouchableOpacity
              style={[styles.button, { opacity: loggingIn ? 0.7 : 1 }]}
              onPress={isLogin ? handleLogin : handleRegister}
              disabled={loggingIn}
              activeOpacity={0.8}
            >
              <LinearGradient
                colors={['#3B82F6', '#2563EB', '#1D4ED8']}
                style={styles.buttonGradient}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
              >
                {loggingIn ? (
                  <ActivityIndicator color="#fff" size="small" />
                ) : (
                  <View style={styles.buttonContent}>
                    <Text style={styles.buttonText}>
                      {isLogin ? 'Giriş Yap' : 'Kayıt Ol'}
                    </Text>
                  </View>
                )}
              </LinearGradient>
            </TouchableOpacity>

            <View style={styles.dividerContainer}>
              <View style={styles.divider} />
              <Text style={styles.dividerText}>VEYA</Text>
              <View style={styles.divider} />
            </View>

            <TouchableOpacity
              style={styles.switchModeButton}
              onPress={() => { setIsLogin(!isLogin); setError(''); }}
              activeOpacity={0.8}
            >
              <LinearGradient
                colors={['rgba(59, 130, 246, 0.1)', 'rgba(37, 99, 235, 0.1)']}
                style={styles.switchButtonGradient}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
              >
                <Text style={styles.switchModeText}>
                  {isLogin
                    ? 'Hesabınız yok mu - Kayıt olun'
                    : 'Zaten hesabınız var - Giriş yapın'}
                </Text>
              </LinearGradient>
            </TouchableOpacity>

            <View style={styles.termsContainer}>
              <Text style={styles.termsText}>
                Giriş yaparak veya kayıt olarak{' '}
                <Text
                  style={styles.termsLink}
                  onPress={() => {
                    Alert.alert('Kullanım Koşulları', 'Kullanım koşullarını görüntülemek için lütfen giriş yapın.');
                  }}
                >
                  Kullanım Koşulları
                </Text>{' '}
                ve{' '}
                <Text
                  style={styles.termsLink}
                  onPress={() => {
                    Alert.alert('Gizlilik Politikası', 'Gizlilik politikasını görüntülemek için lütfen giriş yapın.');
                  }}
                >
                  Gizlilik Politikası
                </Text>'nı kabul etmiş olursunuz.
              </Text>
            </View>

            <Text style={styles.versionText}>VELMORA v1.0.0 | BekTech</Text>
            <Text style={styles.copyrightText}>© 2025 BekTech. Tüm hakları saklıdır.</Text>
          </Animated.View>
        </ScrollView>


      </LinearGradient>
    </KeyboardAvoidingView>
  );
};

/////////////////////////
// STİLLER
/////////////////////////
const styles = StyleSheet.create({
  container: {
    flexGrow: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  splashContainer: {
    flex: 1,
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  splashTitle: {
    fontSize: 48,
    color: '#3B82F6',
    fontWeight: 'bold',
    textShadowColor: 'rgba(59, 130, 246, 0.5)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 10,
    letterSpacing: 2,
    textAlign: 'center',
  },
  loginContainer: {
    width: '100%',
    maxWidth: 400,
    alignItems: 'center',
    paddingVertical: 20,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 30,
  },
  logoWrapper: {
    marginBottom: 15,
    alignItems: 'center',
    justifyContent: 'center',
  },
  logoGradient: {
    width: 70,
    height: 70,
    borderRadius: 35,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 6,
    elevation: 8,
  },
  logoIcon: {
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 3,
  },
  appTitle: {
    fontSize: 42,
    color: '#3B82F6',
    fontWeight: 'bold',
    textShadowColor: 'rgba(59, 130, 246, 0.5)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 10,
    letterSpacing: 2,
    marginBottom: 10,
  },
  appSubtitle: {
    fontSize: 16,
    color: '#9CA3AF',
    letterSpacing: 1,
    textAlign: 'center',
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(239, 68, 68, 0.1)',
    borderRadius: 8,
    padding: 12,
    marginBottom: 20,
    width: '100%',
    borderWidth: 1,
    borderColor: 'rgba(239, 68, 68, 0.3)',
  },
  errorText: {
    color: '#EF4444',
    fontSize: 14,
    flex: 1,
  },
  formContainer: {
    width: '100%',
    marginBottom: 16,
  },
  inputLabel: {
    color: '#9CA3AF',
    fontSize: 14,
    marginBottom: 8,
    fontWeight: '500',
    paddingLeft: 4,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255,255,255,0.05)',
    borderRadius: 12,
    paddingHorizontal: 15,
    borderWidth: 1,
    borderColor: 'rgba(59, 130, 246, 0.3)',
    width: '100%',
  },
  inputIcon: {
    marginRight: 10,
  },
  input: {
    flex: 1,
    color: '#fff',
    padding: 15,
    fontSize: 16,
  },
  visibilityIcon: {
    padding: 10,
  },
  rememberContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
    marginBottom: 20,
  },
  rememberMeButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  checkbox: {
    width: 18,
    height: 18,
    borderWidth: 1,
    borderColor: '#3B82F6',
    borderRadius: 4,
    marginRight: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkboxChecked: {
    backgroundColor: '#3B82F6',
  },
  rememberText: {
    color: '#9CA3AF',
    fontSize: 14,
  },
  forgotButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  forgotText: {
    color: '#3B82F6',
    fontSize: 14,
  },
  button: {
    width: '100%',
    marginBottom: 25,
    borderRadius: 12,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 6,
    elevation: 8,
  },
  buttonGradient: {
    paddingVertical: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    letterSpacing: 0.5,
  },
  dividerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
    marginBottom: 20,
  },
  divider: {
    flex: 1,
    height: 1,
    backgroundColor: 'rgba(255,255,255,0.1)',
  },
  dividerText: {
    color: '#9CA3AF',
    paddingHorizontal: 10,
    fontSize: 12,
    fontWeight: 'bold',
  },
  switchModeButton: {
    width: '100%',
    borderRadius: 12,
    marginBottom: 25,
    overflow: 'hidden',
  },
  switchButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 14,
    borderWidth: 1,
    borderColor: '#3B82F6',
    borderRadius: 12,
  },
  switchModeText: {
    color: '#3B82F6',
    fontSize: 14,
    fontWeight: '500',
  },
  termsContainer: {
    marginBottom: 20,
    paddingHorizontal: 10,
  },
  termsText: {
    color: '#9CA3AF',
    fontSize: 12,
    textAlign: 'center',
    lineHeight: 18,
  },
  termsLink: {
    color: '#3B82F6',
    textDecorationLine: 'underline',
  },
  footerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 10,
  },
  footerLogo: {
    width: 20,
    height: 20,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 8,
  },
  passwordStrengthContainer: {
    width: '100%',
    marginTop: 8,
    marginBottom: 4,
  },
  strengthBarContainer: {
    height: 6,
    width: '100%',
    backgroundColor: '#374151',
    borderRadius: 3,
    overflow: 'hidden',
    marginBottom: 6,
  },
  strengthBar: {
    height: '100%',
    borderRadius: 3,
  },
  strengthLabelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
  },
  strengthText: {
    fontSize: 12,
    fontWeight: '500',
  },
  versionText: {
    color: '#64748B',
    fontSize: 12,
    textAlign: 'center',
    marginTop: 20,
  },
  copyrightText: {
    color: '#64748B',
    fontSize: 10,
    textAlign: 'center',
    marginTop: 5,
    fontStyle: 'italic',
  },
  // Modal stilleri
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContainer: {
    backgroundColor: '#1a1a1a',
    borderRadius: 10,
    padding: 20,
    width: '100%',
    maxWidth: 500,
    maxHeight: '80%',
  },
  modalScrollView: {
    maxHeight: 400,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 15,
    textAlign: 'center',
  },
  modalText: {
    fontSize: 16,
    color: '#ddd',
    marginBottom: 20,
    lineHeight: 24,
  },
  modalButton: {
    backgroundColor: '#3B82F6',
    paddingVertical: 12,
    paddingHorizontal: 30,
    borderRadius: 25,
    alignSelf: 'center',
    marginTop: 10,
  },
  modalButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
  },
});

export default App;
