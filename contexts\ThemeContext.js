import React, { createContext, useState, useContext, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { doc, getDoc, updateDoc } from 'firebase/firestore';
import { auth, db } from '../firebase';

// Tema renkleri
export const lightTheme = {
  background: '#ffffff',
  text: '#000000',
  cardBackground: '#f5f5f5',
  border: '#e0e0e0',
  primary: '#0066CC',
  secondary: '#3498db',
  accent: '#8e44ad',
  error: '#e74c3c',
  success: '#2ecc71',
  warning: '#f39c12',
  info: '#3498db',
  tabBar: '#ffffff',
  tabBarInactive: '#aaaaaa',
  tabBarActive: '#0066CC',
  inputBackground: '#f5f5f5',
  modalBackground: '#ffffff',
  modalOverlay: 'rgba(0,0,0,0.5)',
};

export const darkTheme = {
  background: '#000000',
  text: '#ffffff',
  cardBackground: '#1a1a1a',
  border: '#333333',
  primary: '#0066CC',
  secondary: '#3498db',
  accent: '#8e44ad',
  error: '#e74c3c',
  success: '#2ecc71',
  warning: '#f39c12',
  info: '#3498db',
  tabBar: '#000000',
  tabBarInactive: '#aaaaaa',
  tabBarActive: '#0066CC',
  inputBackground: '#333333',
  modalBackground: '#1a1a1a',
  modalOverlay: 'rgba(0,0,0,0.7)',
};

// Context oluştur
const ThemeContext = createContext();

// Context provider
export const ThemeProvider = ({ children }) => {
  // Her zaman koyu tema kullan
  const [isDarkMode, setIsDarkMode] = useState(true); // Varsayılan olarak koyu tema
  const [theme, setTheme] = useState(darkTheme);
  const [isLoading, setIsLoading] = useState(true);

  // Tema tercihini AsyncStorage'dan yükle - artık sadece koyu tema var
  useEffect(() => {
    const loadThemePreference = async () => {
      try {
        // Her zaman koyu tema kullan
        setIsDarkMode(true);
        setTheme(darkTheme);

        // Eski tema tercihini sil
        await AsyncStorage.removeItem('themePreference');

        // Kullanıcı oturum açmışsa Firestore'daki tema tercihini güncelle
        if (auth.currentUser) {
          const userRef = doc(db, 'users', auth.currentUser.uid);
          try {
            // Firestore'daki tema tercihini koyu tema olarak güncelle
            await updateDoc(userRef, {
              themePreference: 'dark'
            });
          } catch (error) {
            console.error('Firestore tema tercihi güncellenirken hata:', error);
          }
        }
      } catch (error) {
        console.error('Tema tercihi yüklenirken hata:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadThemePreference();
  }, []);

  // Tema değiştirme fonksiyonu - artık sadece koyu tema var
  const toggleTheme = async () => {
    // Tema değiştirme devre dışı bırakıldı
    console.log('Tema değiştirme devre dışı bırakıldı. Uygulama her zaman koyu tema kullanıyor.');
    return;
  };

  return (
    <ThemeContext.Provider value={{ theme, isDarkMode, toggleTheme, isLoading }}>
      {children}
    </ThemeContext.Provider>
  );
};

// Custom hook
export const useTheme = () => useContext(ThemeContext);
