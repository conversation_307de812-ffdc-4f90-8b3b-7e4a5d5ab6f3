import React from 'react';
import { View } from 'react-native';
import Svg, { Path, Circle, G, LinearGradient, Stop, Defs, Text } from 'react-native-svg';

const VelmoraLogo = ({ width = 200, height = 200 }) => {
  return (
    <View style={{ width, height }}>
      <Svg width={width} height={height} viewBox="0 0 200 200">
        <Defs>
          <LinearGradient id="gradMain" x1="0%" y1="0%" x2="100%" y2="100%">
            <Stop offset="0%" stopColor="#0066CC" stopOpacity="1" />
            <Stop offset="100%" stopColor="#1DA1F2" stopOpacity="1" />
          </LinearGradient>
          <LinearGradient id="gradGlow" x1="0%" y1="0%" x2="100%" y2="100%">
            <Stop offset="0%" stopColor="#0066CC" stopOpacity="0.7" />
            <Stop offset="100%" stopColor="#1DA1F2" stopOpacity="0.7" />
          </LinearGradient>
        </Defs>
        
        {/* Outer Glow */}
        <Circle cx="100" cy="100" r="95" fill="none" stroke="url(#gradGlow)" strokeWidth="6" opacity="0.5" />
        
        {/* Main Circle */}
        <Circle cx="100" cy="100" r="85" fill="none" stroke="url(#gradMain)" strokeWidth="4" />
        
        {/* Inner Circle */}
        <Circle cx="100" cy="100" r="70" fill="none" stroke="url(#gradMain)" strokeWidth="2" opacity="0.8" />
        
        {/* V Letter */}
        <Path
          d="M70,60 L100,140 L130,60"
          fill="none"
          stroke="url(#gradMain)"
          strokeWidth="8"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        
        {/* Music Note */}
        <G transform="translate(130, 100) scale(0.8)">
          <Path
            d="M0,0 C5,0 10,-5 10,-15 C10,-25 5,-30 0,-30 C-5,-30 -10,-25 -10,-15 C-10,-5 -5,0 0,0 Z"
            fill="url(#gradMain)"
          />
          <Path
            d="M0,-30 L0,-80"
            stroke="url(#gradMain)"
            strokeWidth="4"
            strokeLinecap="round"
          />
        </G>
        
        {/* Poem Symbol (Pen) */}
        <G transform="translate(70, 100) scale(0.8)">
          <Path
            d="M-10,15 L10,-15 L5,-20 L-15,10 Z"
            fill="url(#gradMain)"
          />
          <Path
            d="M-15,10 L-10,15"
            stroke="url(#gradMain)"
            strokeWidth="2"
          />
        </G>
        
        {/* Sparkles */}
        <Circle cx="50" cy="50" r="2" fill="#1DA1F2" />
        <Circle cx="150" cy="50" r="2" fill="#1DA1F2" />
        <Circle cx="50" cy="150" r="2" fill="#1DA1F2" />
        <Circle cx="150" cy="150" r="2" fill="#1DA1F2" />
        <Circle cx="100" cy="30" r="2" fill="#1DA1F2" />
        <Circle cx="100" cy="170" r="2" fill="#1DA1F2" />
        <Circle cx="30" cy="100" r="2" fill="#1DA1F2" />
        <Circle cx="170" cy="100" r="2" fill="#1DA1F2" />
      </Svg>
    </View>
  );
};

export default VelmoraLogo;
