import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  FlatList,
  StyleSheet,
  TouchableOpacity,
  Image,
  ActivityIndicator,
  RefreshControl,
  ToastAndroid
} from 'react-native';
import {
  collection,
  query,
  where,
  orderBy,
  getDocs,
  doc,
  getDoc
} from 'firebase/firestore';
import { db, auth } from '../firebase';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import { useToast } from '../contexts/ToastContext';

// Avatar mapping
const avatarMap = {
  avatar1: require('../assets/avatar1.png'),
  avatar2: require('../assets/avatar2.png'),
  avatar3: require('../assets/avatar3.png'),
  avatar4: require('../assets/avatar4.png'),
  avatar5: require('../assets/avatar5.png'),
  avatar6: require('../assets/avatar6.png'),
  avatar7: require('../assets/avatar7.png'),
  avatar8: require('../assets/avatar8.png'),
  avatar9: require('../assets/avatar9.png'),
  avatar10: require('../assets/avatar10.png'),
  avatar11: require('../assets/avatar11.png'),
  avatar12: require('../assets/avatar12.png'),
  avatar13: require('../assets/avatar13.png'),
  avatar14: require('../assets/avatar14.png'),
};

export default function LikedPostsScreen() {
  const [likedPosts, setLikedPosts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  const navigation = useNavigation();
  const currentUser = auth.currentUser;
  const { theme } = useTheme();
  const { translations } = useLanguage();
  const { showToast } = useToast();

  // Beğenilen gönderileri yükle
  useEffect(() => {
    if (!currentUser) return;

    loadLikedPosts();
  }, [currentUser]);

  // Beğenilen gönderileri yükle
  const loadLikedPosts = async () => {
    if (!currentUser) return;

    setLoading(true);
    try {
      // Kullanıcının beğendiği gönderileri bul
      // İndeks sorunu nedeniyle tüm gönderileri çekip JavaScript'te filtreleme yapacağız
      const postsRef = collection(db, 'posts');
      const q = query(postsRef);

      const snapshot = await getDocs(q);

      if (snapshot.empty) {
        setLikedPosts([]);
        setLoading(false);
        return;
      }

      // Beğenilen gönderilerin detaylarını al
      const likedPostsData = [];

      for (const docSnapshot of snapshot.docs) {
        const postData = docSnapshot.data();

        // Kullanıcının beğendiği gönderileri filtrele
        try {
          if (!postData.likedBy || !Array.isArray(postData.likedBy)) {
            continue; // Geçersiz veri, atla
          }

          // indexOf ve includes yerine daha güvenli bir yöntem kullan
          const isLiked = postData.likedBy.filter(id => id && typeof id === 'string').some(id => id === currentUser.uid);
          if (!isLiked) {
            continue; // Bu gönderi beğenilmemiş, atla
          }
        } catch (error) {
          console.error('Beğeni kontrolü sırasında hata:', error);
          continue; // Hata durumunda atla
        }

        // Gönderi sahibinin bilgilerini al
        const userRef = doc(db, 'users', postData.uid);
        const userSnap = await getDoc(userRef);
        const userData = userSnap.exists() ? userSnap.data() : {};

        likedPostsData.push({
          id: docSnapshot.id,
          ...postData,
          username: userData.username || 'Kullanıcı',
          profilePic: userData.profilePic || 'avatar1'
        });
      }

      // Tarihe göre sırala (en yeni en üstte)
      likedPostsData.sort((a, b) => {
        // createdAt null olabilir
        if (!a.createdAt) return 1;
        if (!b.createdAt) return -1;

        // toDate metodu var mı kontrol et
        const dateA = a.createdAt.toDate ? a.createdAt.toDate() : new Date(a.createdAt);
        const dateB = b.createdAt.toDate ? b.createdAt.toDate() : new Date(b.createdAt);

        return dateB - dateA;
      });

      setLikedPosts(likedPostsData);
    } catch (error) {
      console.error('Beğenilen gönderiler yüklenirken hata:', error);
      showToast({
        message: translations.likedPostsError || 'Beğenilen gönderiler yüklenirken bir hata oluştu.',
        type: 'error'
      });
    }

    setLoading(false);
  };

  // Yenileme işlemi
  const onRefresh = () => {
    setRefreshing(true);
    loadLikedPosts().then(() => setRefreshing(false));
  };

  // Gönderi öğesi render
  const renderPostItem = ({ item }) => {
    return (
      <View style={[styles.postItem, { backgroundColor: theme.cardBackground }]}>
        <View style={styles.postHeader}>
          <TouchableOpacity
            style={styles.userInfo}
            onPress={() => {
              if (item.uid === currentUser?.uid) {
                navigation.navigate('Profil');
              } else {
                navigation.navigate('OtherProfile', { uid: item.uid });
              }
            }}
          >
            <Image
              source={avatarMap[item.profilePic] || require('../assets/default-avatar.png')}
              style={styles.avatar}
            />
            <Text style={[styles.username, { color: theme.text }]}>{item.username}</Text>
          </TouchableOpacity>

          <View style={styles.likeInfo}>
            <Ionicons name="heart" size={20} color="#e74c3c" />
          </View>
        </View>

        <TouchableOpacity
          style={styles.postContent}
          onPress={() => navigation.navigate('Comments', { postId: item.id })}
        >
          <Text style={[styles.postText, { color: theme.text }]} numberOfLines={3}>
            {item.content}
          </Text>
        </TouchableOpacity>

        <View style={styles.postFooter}>
          <View style={styles.postStats}>
            <View style={styles.statItem}>
              <Ionicons name="heart" size={16} color={theme.error} />
              <Text style={[styles.statText, { color: theme.text }]}>{item.likes || 0}</Text>
            </View>
            <View style={styles.statItem}>
              <Ionicons name="chatbubble-outline" size={16} color={theme.text} />
              <Text style={[styles.statText, { color: theme.text }]}>{item.comments || 0}</Text>
            </View>
          </View>

          <Text style={[styles.postDate, { color: theme.text }]}>
            {item.createdAt ? new Date(item.createdAt.toDate()).toLocaleDateString() : ''}
          </Text>
        </View>
      </View>
    );
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.background }]}>
      {/* Üst Bar */}
      <View style={[styles.header, { borderBottomColor: theme.border }]}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color={theme.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: theme.text }]}>{translations.likedPosts}</Text>
        <View style={styles.rightSpace} />
      </View>

      {loading ? (
        <ActivityIndicator size="large" color={theme.primary} style={styles.loader} />
      ) : likedPosts.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Ionicons name="heart-outline" size={64} color={theme.text} />
          <Text style={[styles.emptyText, { color: theme.text }]}>{translations.noLikedPosts || 'Beğenilen gönderi bulunmamaktadır'}</Text>
        </View>
      ) : (
        <FlatList
          data={likedPosts}
          renderItem={renderPostItem}
          keyExtractor={item => item.id}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              tintColor={theme.text}
            />
          }
          contentContainerStyle={styles.listContainer}
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: 30
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 15,
    paddingVertical: 10,
    borderBottomWidth: 1
  },
  backButton: {
    padding: 5
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold'
  },
  rightSpace: {
    width: 24
  },
  loader: {
    marginTop: 20
  },
  listContainer: {
    paddingBottom: 20,
    paddingHorizontal: 10
  },
  postItem: {
    borderRadius: 10,
    marginVertical: 8,
    padding: 15,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4
  },
  postHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 10
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 10,
    borderWidth: 1,
    borderColor: '#0066CC'
  },
  username: {
    fontSize: 16,
    fontWeight: 'bold'
  },
  likeInfo: {
    padding: 5
  },
  postContent: {
    marginBottom: 10
  },
  postText: {
    fontSize: 15,
    lineHeight: 20
  },
  postFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 5
  },
  postStats: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 15
  },
  statText: {
    marginLeft: 5,
    fontSize: 14
  },
  postDate: {
    fontSize: 12
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20
  },
  emptyText: {
    fontSize: 16,
    marginTop: 10,
    textAlign: 'center'
  }
});
