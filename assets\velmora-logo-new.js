import React from 'react';
import { View } from 'react-native';
import Svg, { Path, Circle, G, LinearGradient, Stop, Defs, Text, Ellipse, Rect } from 'react-native-svg';

const VelmoraLogoNew = ({ width = 200, height = 200 }) => {
  return (
    <View style={{ width, height }}>
      <Svg width={width} height={height} viewBox="0 0 200 200">
        <Defs>
          <LinearGradient id="gradMain" x1="0%" y1="0%" x2="100%" y2="100%">
            <Stop offset="0%" stopColor="#1E3A8A" stopOpacity="1" />
            <Stop offset="100%" stopColor="#3B82F6" stopOpacity="1" />
          </LinearGradient>
          <LinearGradient id="gradGlow" x1="0%" y1="0%" x2="100%" y2="100%">
            <Stop offset="0%" stopColor="#1E3A8A" stopOpacity="0.7" />
            <Stop offset="100%" stopColor="#3B82F6" stopOpacity="0.7" />
          </LinearGradient>
          <LinearGradient id="gradAccent" x1="0%" y1="0%" x2="100%" y2="100%">
            <Stop offset="0%" stopColor="#F59E0B" stopOpacity="1" />
            <Stop offset="100%" stopColor="#F97316" stopOpacity="1" />
          </LinearGradient>
        </Defs>
        
        {/* Background Circle with Glow */}
        <Circle cx="100" cy="100" r="95" fill="rgba(30, 58, 138, 0.1)" />
        <Circle cx="100" cy="100" r="85" fill="none" stroke="url(#gradGlow)" strokeWidth="2" />
        
        {/* Main Circle */}
        <Circle cx="100" cy="100" r="75" fill="none" stroke="url(#gradMain)" strokeWidth="4" />
        
        {/* Decorative Elements */}
        <Circle cx="100" cy="100" r="65" fill="none" stroke="url(#gradMain)" strokeWidth="1" opacity="0.6" />
        <Circle cx="100" cy="100" r="55" fill="none" stroke="url(#gradMain)" strokeWidth="1" opacity="0.4" />
        
        {/* V Letter - More Modern and Bold */}
        <Path
          d="M65,55 L100,145 L135,55"
          fill="none"
          stroke="url(#gradMain)"
          strokeWidth="10"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        
        {/* Music Note - More Stylized */}
        <G transform="translate(135, 100) scale(0.9)">
          <Path
            d="M0,0 C6,0 12,-6 12,-18 C12,-30 6,-36 0,-36 C-6,-36 -12,-30 -12,-18 C-12,-6 -6,0 0,0 Z"
            fill="url(#gradAccent)"
          />
          <Path
            d="M0,-36 L0,-90"
            stroke="url(#gradAccent)"
            strokeWidth="5"
            strokeLinecap="round"
          />
        </G>
        
        {/* Poem Symbol (Pen) - More Elegant */}
        <G transform="translate(65, 100) scale(0.9)">
          <Path
            d="M-12,18 L12,-18 L6,-24 L-18,12 Z"
            fill="url(#gradAccent)"
          />
          <Path
            d="M-18,12 L-12,18"
            stroke="url(#gradAccent)"
            strokeWidth="3"
          />
        </G>
        
        {/* Sparkles and Stars */}
        <Circle cx="50" cy="50" r="3" fill="#F59E0B" />
        <Circle cx="150" cy="50" r="3" fill="#F59E0B" />
        <Circle cx="50" cy="150" r="3" fill="#F59E0B" />
        <Circle cx="150" cy="150" r="3" fill="#F59E0B" />
        
        {/* Star Bursts */}
        <G transform="translate(30, 70)">
          <Path d="M0,0 L5,5 M0,5 L5,0 M2.5,-3 L2.5,8" stroke="#F59E0B" strokeWidth="1.5" />
        </G>
        <G transform="translate(170, 70)">
          <Path d="M0,0 L5,5 M0,5 L5,0 M2.5,-3 L2.5,8" stroke="#F59E0B" strokeWidth="1.5" />
        </G>
        <G transform="translate(100, 25)">
          <Path d="M0,0 L5,5 M0,5 L5,0 M2.5,-3 L2.5,8" stroke="#F59E0B" strokeWidth="1.5" />
        </G>
        
        {/* Subtle Glow Effect */}
        <Circle cx="100" cy="100" r="100" fill="url(#gradMain)" opacity="0.05" />
      </Svg>
    </View>
  );
};

export default VelmoraLogoNew;
