import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Image,
  ActivityIndicator,
  RefreshControl,
  ScrollView,
  Animated
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { collection, query, orderBy, limit, getDocs, where, Timestamp, onSnapshot } from 'firebase/firestore';
import { db, auth } from '../firebase';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import { listenToUserPopularity } from '../utils/popularityUtils';

// Avatar mapping
const avatarMap = {
  avatar1: require('../assets/avatar1.png'),
  avatar2: require('../assets/avatar2.png'),
  avatar3: require('../assets/avatar3.png'),
  avatar4: require('../assets/avatar4.png'),
  avatar5: require('../assets/avatar5.png'),
  avatar6: require('../assets/avatar6.png'),
  avatar7: require('../assets/avatar7.png'),
  avatar8: require('../assets/avatar8.png'),
  avatar9: require('../assets/avatar9.png'),
  avatar10: require('../assets/avatar10.png'),
  avatar11: require('../assets/avatar11.png'),
  avatar12: require('../assets/avatar12.png'),
  avatar13: require('../assets/avatar13.png'),
  avatar14: require('../assets/avatar14.png'),
};

// Leaderboard Section Component
const LeaderboardSection = ({ title, users, onUserPress, loading }) => {
  // Kullanıcı popülerlik değerlerini canlı olarak takip etmek için state
  const [liveUsers, setLiveUsers] = useState(users);
  // Animasyon değerleri
  const animValues = useRef({}).current;

  // Kullanıcı listesi değiştiğinde, canlı takip başlat
  useEffect(() => {
    setLiveUsers(users);

    // Her kullanıcı için popülerlik dinleyicileri oluştur
    const unsubscribes = users.map(user => {
      // Animasyon değeri oluştur
      if (!animValues[user.id]) {
        animValues[user.id] = new Animated.Value(1);
      }

      // Popülerlik değişimini dinle
      return listenToUserPopularity(user.id, (newPopularity) => {
        // Popülerlik değiştiğinde kullanıcı listesini güncelle
        setLiveUsers(prevUsers => {
          const updatedUsers = [...prevUsers];
          const userIndex = updatedUsers.findIndex(u => u.id === user.id);

          if (userIndex !== -1) {
            // Popülerlik değiştiğinde animasyon yap
            if (updatedUsers[userIndex].popularity !== newPopularity) {
              Animated.sequence([
                Animated.timing(animValues[user.id], {
                  toValue: 1.1,
                  duration: 300,
                  useNativeDriver: true
                }),
                Animated.timing(animValues[user.id], {
                  toValue: 1,
                  duration: 300,
                  useNativeDriver: true
                })
              ]).start();

              // Kullanıcı verisini güncelle
              updatedUsers[userIndex] = {
                ...updatedUsers[userIndex],
                popularity: newPopularity
              };

              // Popülerliğe göre sırala
              updatedUsers.sort((a, b) => (b.popularity || 0) - (a.popularity || 0));
            }
          }

          return updatedUsers;
        });
      });
    });

    // Temizleme fonksiyonu
    return () => {
      unsubscribes.forEach(unsub => unsub && unsub());
    };
  }, [users]);

  return (
    <View style={styles.sectionContainer}>
      <Text style={styles.sectionTitle}>{title}</Text>
      {loading ? (
        <ActivityIndicator size="small" color="#1E3A8A" style={styles.loader} />
      ) : liveUsers.length > 0 ? (
        <FlatList
          data={liveUsers}
          keyExtractor={(item) => item.id}
          scrollEnabled={false}
          renderItem={({ item, index }) => (
            <TouchableOpacity
              style={styles.userItem}
              onPress={() => onUserPress(item)}
              activeOpacity={0.7}
            >
              <Text style={styles.rankNumber}>#{index + 1}</Text>
              <Image
                source={
                  item.profilePic
                    ? avatarMap[item.profilePic] || require('../assets/default-avatar.png')
                    : require('../assets/default-avatar.png')
                }
                style={styles.userAvatar}
              />
              <Animated.View
                style={[
                  styles.userInfo,
                  { transform: [{ scale: animValues[item.id] || 1 }] }
                ]}
              >
                <Text style={styles.userName}>{item.username}</Text>
                <Text style={styles.userPopularity}>
                  {item.popularity || 0} popülerlik
                </Text>
              </Animated.View>
              <Ionicons name="chevron-forward" size={20} color="#666" />
            </TouchableOpacity>
          )}
        />
      ) : (
        <Text style={styles.noUsersText}>No users found</Text>
      )}
    </View>
  );
};

const LeaderboardScreen = () => {
  const navigation = useNavigation();
  const { isDarkMode } = useTheme();
  const { translations } = useLanguage();

  const [allTimePopularUsers, setAllTimePopularUsers] = useState([]);
  const [dailyStars, setDailyStars] = useState([]);
  const [hourlyTrends, setHourlyTrends] = useState([]);
  const [mostFollowedUsers, setMostFollowedUsers] = useState([]);

  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // Gerçek zamanlı dinleyicileri saklamak için
  const leaderboardListeners = useRef({});

  const fetchLeaderboardData = () => {
    try {
      setLoading(true);

      // Önceki dinleyicileri temizle
      Object.values(leaderboardListeners.current).forEach(unsub => {
        if (typeof unsub === 'function') unsub();
      });

      // Yeni dinleyicileri saklamak için objeyi sıfırla
      leaderboardListeners.current = {};

      // All-time most popular users - Gerçek zamanlı dinleme
      const popularUsersQuery = query(
        collection(db, 'users'),
        orderBy('popularity', 'desc'),
        limit(5)
      );

      leaderboardListeners.current.allTime = onSnapshot(popularUsersQuery, (snapshot) => {
        const popularUsers = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));
        setAllTimePopularUsers(popularUsers);
      }, (error) => {
        console.error("All-time popular users dinleme hatası:", error);
      });

      // Daily stars (last 24 hours) - Gerçek zamanlı dinleme
      const dailyStarsQuery = query(
        collection(db, 'users'),
        orderBy('dailyPopularity', 'desc'),
        limit(5)
      );

      leaderboardListeners.current.daily = onSnapshot(dailyStarsQuery, (snapshot) => {
        const dailyStarsUsers = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));
        setDailyStars(dailyStarsUsers);
      }, (error) => {
        console.error("Daily stars dinleme hatası:", error);
      });

      // Hourly trends (last hour) - Gerçek zamanlı dinleme
      const hourlyTrendsQuery = query(
        collection(db, 'users'),
        orderBy('hourlyPopularity', 'desc'),
        limit(5)
      );

      leaderboardListeners.current.hourly = onSnapshot(hourlyTrendsQuery, (snapshot) => {
        const hourlyTrendsUsers = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));
        setHourlyTrends(hourlyTrendsUsers);
      }, (error) => {
        console.error("Hourly trends dinleme hatası:", error);
      });

      // Most followed users - Gerçek zamanlı dinleme
      const mostFollowedQuery = query(
        collection(db, 'users'),
        orderBy('followerCount', 'desc'),
        limit(5)
      );

      leaderboardListeners.current.followed = onSnapshot(mostFollowedQuery, (snapshot) => {
        const mostFollowedUsers = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));
        setMostFollowedUsers(mostFollowedUsers);

        // Tüm veriler yüklendikten sonra loading durumunu kapat
        setLoading(false);
        setRefreshing(false);
      }, (error) => {
        console.error("Most followed users dinleme hatası:", error);
        setLoading(false);
        setRefreshing(false);
      });

    } catch (error) {
      console.error("Error fetching leaderboard data:", error);
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchLeaderboardData();

    // Component unmount olduğunda dinleyicileri temizle
    return () => {
      Object.values(leaderboardListeners.current).forEach(unsub => {
        if (typeof unsub === 'function') unsub();
      });
    };
  }, []);

  const handleRefresh = () => {
    setRefreshing(true);
    fetchLeaderboardData();
  };

  const navigateToUserProfile = (user) => {
    if (user.id === auth.currentUser?.uid) {
      navigation.navigate('Profil');
    } else {
      navigation.navigate('OtherProfile', { userId: user.id });
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: isDarkMode ? '#000' : '#fff' }]}>
      <View style={styles.header}>
        <Ionicons name="list-outline" size={24} color="#3B82F6" style={{ marginRight: 10 }} />
        <Text style={styles.headerTitle}>{translations.leaderboard || "Liderlik Tablosu"}</Text>
      </View>

      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={['#1E3A8A']}
            tintColor={isDarkMode ? '#fff' : '#1E3A8A'}
          />
        }
      >
        {/* All-time most popular */}
        <LeaderboardSection
          title={translations.allTimeMostPopular || "All-Time Most Popular"}
          users={allTimePopularUsers}
          onUserPress={navigateToUserProfile}
          loading={loading && !refreshing}
        />

        {/* Daily Stars */}
        <LeaderboardSection
          title={translations.dailyStars || "Günün Yıldızları"}
          users={dailyStars}
          onUserPress={navigateToUserProfile}
          loading={loading && !refreshing}
        />

        {/* Hourly Trends */}
        <LeaderboardSection
          title={translations.hourlyTrends || "Saatin Trendleri"}
          users={hourlyTrends}
          onUserPress={navigateToUserProfile}
          loading={loading && !refreshing}
        />

        {/* Most Followed */}
        <LeaderboardSection
          title={translations.mostFollowed || "En Çok Takip Edilenler"}
          users={mostFollowedUsers}
          onUserPress={navigateToUserProfile}
          loading={loading && !refreshing}
        />
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  header: {
    backgroundColor: '#121212',
    paddingVertical: 15,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#333',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTitle: {
    color: '#fff',
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  scrollView: {
    flex: 1,
  },
  sectionContainer: {
    marginVertical: 10,
    paddingHorizontal: 16,
    backgroundColor: '#121212',
    borderRadius: 10,
    marginHorizontal: 10,
    paddingVertical: 12,
    borderWidth: 1,
    borderColor: '#333',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 10,
  },
  userItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
  },
  rankNumber: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#3B82F6',
    width: 40,
  },
  userAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 10,
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#fff',
  },
  userPopularity: {
    fontSize: 12,
    color: '#aaa',
  },
  noUsersText: {
    color: '#666',
    textAlign: 'center',
    marginVertical: 10,
  },
  loader: {
    marginVertical: 20,
  },
});

export default LeaderboardScreen;
