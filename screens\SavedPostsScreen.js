import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  FlatList,
  StyleSheet,
  TouchableOpacity,
  Image,
  ActivityIndicator,
  RefreshControl,
  ToastAndroid
} from 'react-native';
import {
  collection,
  query,
  where,
  orderBy,
  getDocs,
  doc,
  getDoc,
  deleteDoc
} from 'firebase/firestore';
import { db, auth } from '../firebase';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';

// Avatar mapping
const avatarMap = {
  avatar1: require('../assets/avatar1.png'),
  avatar2: require('../assets/avatar2.png'),
  avatar3: require('../assets/avatar3.png'),
  avatar4: require('../assets/avatar4.png'),
  avatar5: require('../assets/avatar5.png'),
  avatar6: require('../assets/avatar6.png'),
  avatar7: require('../assets/avatar7.png'),
  avatar8: require('../assets/avatar8.png'),
  avatar9: require('../assets/avatar9.png'),
  avatar10: require('../assets/avatar10.png'),
  avatar11: require('../assets/avatar11.png'),
  avatar12: require('../assets/avatar12.png'),
  avatar13: require('../assets/avatar13.png'),
  avatar14: require('../assets/avatar14.png'),
};

export default function SavedPostsScreen() {
  const [savedPosts, setSavedPosts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  const navigation = useNavigation();
  const currentUser = auth.currentUser;
  const { theme } = useTheme();
  const { translations } = useLanguage();

  // Kaydedilen gönderileri yükle
  useEffect(() => {
    if (!currentUser) return;

    loadSavedPosts();
  }, [currentUser]);

  // Kaydedilen gönderileri yükle
  const loadSavedPosts = async () => {
    if (!currentUser) return;

    setLoading(true);
    try {
      // Kullanıcının kaydettiği gönderileri al
      const savedRef = collection(db, 'savedPosts');
      // Not: Bu sorgu için Firestore'da bir indeks oluşturmanız gerekebilir
      // Eğer hata alırsanız, hata mesajındaki URL'yi kullanarak indeksi oluşturun
      // Alternatif olarak, aşağıdaki sorguyu kullanabilirsiniz (indeks gerektirmez):
      const q = query(
        savedRef,
        where('userId', '==', currentUser.uid)
        // orderBy('savedAt', 'desc') - indeks gerektirir
      );

      const snapshot = await getDocs(q);

      if (snapshot.empty) {
        setSavedPosts([]);
        setLoading(false);
        return;
      }

      // Kaydedilen gönderilerin detaylarını al
      const savedPostsData = [];

      for (const docSnapshot of snapshot.docs) {
        const savedData = docSnapshot.data();
        const postRef = doc(db, 'posts', savedData.postId);
        const postSnap = await getDoc(postRef);

        if (postSnap.exists()) {
          const postData = postSnap.data();

          // Gönderi sahibinin bilgilerini al
          const userRef = doc(db, 'users', postData.uid);
          const userSnap = await getDoc(userRef);
          const userData = userSnap.exists() ? userSnap.data() : {};

          savedPostsData.push({
            id: docSnapshot.id,
            postId: savedData.postId,
            savedAt: savedData.savedAt || { toDate: () => new Date() },
            post: {
              id: postSnap.id,
              ...postData,
              username: userData.username || 'Kullanıcı',
              profilePic: userData.profilePic || 'avatar1'
            }
          });
        }
      }

      setSavedPosts(savedPostsData);
    } catch (error) {
      console.error('Kaydedilen gönderiler yüklenirken hata:', error);
      ToastAndroid.show('Kaydedilen gönderiler yüklenirken bir hata oluştu.', ToastAndroid.SHORT);
    }

    setLoading(false);
  };

  // Yenileme işlemi
  const onRefresh = () => {
    setRefreshing(true);
    loadSavedPosts().then(() => setRefreshing(false));
  };

  // Kayıt kaldır
  const handleRemoveSaved = async (savedId) => {
    try {
      await deleteDoc(doc(db, 'savedPosts', savedId));
      setSavedPosts(prev => prev.filter(item => item.id !== savedId));
      ToastAndroid.show('Gönderi kayıtlardan kaldırıldı.', ToastAndroid.SHORT);
    } catch (error) {
      console.error('Kayıt kaldırma hatası:', error);
      ToastAndroid.show('Gönderi kayıtlardan kaldırılırken bir hata oluştu.', ToastAndroid.SHORT);
    }
  };

  // Gönderi öğesi render
  const renderPostItem = ({ item }) => {
    const post = item.post;

    return (
      <View style={[styles.postItem, { backgroundColor: theme.cardBackground }]}>
        <View style={styles.postHeader}>
          <TouchableOpacity
            style={styles.userInfo}
            onPress={() => {
              if (post.uid === currentUser?.uid) {
                navigation.navigate('Profil');
              } else {
                navigation.navigate('OtherProfile', { uid: post.uid });
              }
            }}
          >
            <Image
              source={avatarMap[post.profilePic] || require('../assets/default-avatar.png')}
              style={styles.avatar}
            />
            <Text style={[styles.username, { color: theme.text }]}>{post.username}</Text>
          </TouchableOpacity>

          <TouchableOpacity
            onPress={() => handleRemoveSaved(item.id)}
            style={styles.removeButton}
          >
            <Ionicons name="bookmark" size={24} color={theme.primary} />
          </TouchableOpacity>
        </View>

        <TouchableOpacity
          style={styles.postContent}
          onPress={() => navigation.navigate('Comments', { postId: post.id })}
        >
          <Text style={[styles.postText, { color: theme.text }]} numberOfLines={3}>
            {post.content}
          </Text>
        </TouchableOpacity>

        <View style={styles.postFooter}>
          <View style={styles.postStats}>
            <View style={styles.statItem}>
              <Ionicons name="heart" size={16} color={theme.error} />
              <Text style={[styles.statText, { color: theme.text }]}>{post.likes || 0}</Text>
            </View>
            <View style={styles.statItem}>
              <Ionicons name="chatbubble-outline" size={16} color={theme.text} />
              <Text style={[styles.statText, { color: theme.text }]}>{post.comments || 0}</Text>
            </View>
          </View>

          <Text style={[styles.savedDate, { color: theme.text }]}>
            {item.savedAt && item.savedAt.toDate ? new Date(item.savedAt.toDate()).toLocaleDateString() : new Date().toLocaleDateString()}
          </Text>
        </View>
      </View>
    );
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.background }]}>
      {/* Üst Bar */}
      <View style={[styles.header, { borderBottomColor: theme.border }]}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color={theme.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: theme.text }]}>{translations.savedPosts}</Text>
        <View style={styles.rightSpace} />
      </View>

      {loading ? (
        <ActivityIndicator size="large" color={theme.primary} style={styles.loader} />
      ) : savedPosts.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Ionicons name="bookmark-outline" size={64} color={theme.text} />
          <Text style={[styles.emptyText, { color: theme.text }]}>{translations.noSavedPosts || 'Kaydedilen gönderi bulunmamaktadır'}</Text>
        </View>
      ) : (
        <FlatList
          data={savedPosts}
          renderItem={renderPostItem}
          keyExtractor={item => item.id}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              tintColor={theme.text}
            />
          }
          contentContainerStyle={styles.listContainer}
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: 30
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 15,
    paddingVertical: 10,
    borderBottomWidth: 1
  },
  backButton: {
    padding: 5
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold'
  },
  rightSpace: {
    width: 24
  },
  loader: {
    marginTop: 20
  },
  listContainer: {
    paddingBottom: 20,
    paddingHorizontal: 10
  },
  postItem: {
    borderRadius: 10,
    marginVertical: 8,
    padding: 15,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4
  },
  postHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 10
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 10,
    borderWidth: 1,
    borderColor: '#0066CC'
  },
  username: {
    fontSize: 16,
    fontWeight: 'bold'
  },
  removeButton: {
    padding: 5
  },
  postContent: {
    marginBottom: 10
  },
  postText: {
    fontSize: 15,
    lineHeight: 20
  },
  postFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 5
  },
  postStats: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 15
  },
  statText: {
    marginLeft: 5,
    fontSize: 14
  },
  savedDate: {
    fontSize: 12
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20
  },
  emptyText: {
    fontSize: 16,
    marginTop: 10,
    textAlign: 'center'
  }
});
