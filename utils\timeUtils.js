/**
 * <PERSON><PERSON><PERSON> tarihi "şu kadar zaman önce" formatında döndürür
 * @param {Date|number|string} date - <PERSON><PERSON><PERSON> nes<PERSON>, timestamp veya tarih string'i
 * @param {Date} [now] - Karşılaştırma için <PERSON>aman (opsiyonel)
 * @returns {string} Formatlanmış zaman string'i
 */
export function timeAgo(date, now = new Date()) {
  if (!date) return '';

  // Tarih formatını düzenle
  const dateObj = typeof date === 'object' ? date : 
                 (typeof date === 'number' ? new Date(date) : 
                 (date.seconds ? new Date(date.seconds * 1000) : new Date(date)));
  
  const seconds = Math.floor((now - dateObj) / 1000);
  
  // Negatif zaman kontrolü
  if (seconds < 0) {
    return 'şimdi';
  }

  // Zaman aralıkları
  const intervals = {
    yıl: 31536000,
    ay: 2592000,
    hafta: 604800,
    gün: 86400,
    saat: 3600,
    dakika: 60,
    saniye: 1
  };

  // En uygun zaman aralığını bul
  for (const [unit, secondsInUnit] of Object.entries(intervals)) {
    const interval = Math.floor(seconds / secondsInUnit);
    
    if (interval >= 1) {
      return `${interval} ${unit}${interval > 1 && unit !== 'ay' ? '' : ''} önce`;
    }
  }
  
  return 'şimdi';
}
