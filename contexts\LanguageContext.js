import React, { createContext, useState, useContext, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { doc, getDoc, updateDoc } from 'firebase/firestore';
import { auth, db } from '../firebase';
import * as Localization from 'expo-localization';

// Dil çevirileri
export const translationData = {
  tr: {
    // General
    appName: 'VELMORA',
    loading: 'Yükleniyor...',
    save: '<PERSON><PERSON>',
    cancel: '<PERSON>pta<PERSON>',
    delete: 'Sil',
    edit: '<PERSON><PERSON><PERSON><PERSON>',
    close: '<PERSON>pat',
    confirm: '<PERSON><PERSON><PERSON>',
    yes: '<PERSON><PERSON>',
    no: '<PERSON><PERSON><PERSON>',
    ok: 'Tamam',
    error: 'Hata',
    success: 'Başarılı',
    warning: 'Uyar<PERSON>',
    info: 'Bilgi',
    welcome: 'Hoş Geldiniz',
    joinUs: 'Aramıza Katılın',
    enterEmail: 'Lütfen e-posta adresinizi giriniz.',
    enterUsername: '<PERSON><PERSON><PERSON><PERSON> kullanıcı adınızı giriniz.',
    invalidEmail: 'Geçersiz e-posta adresi.',
    fillAllFields: 'Lütfen tüm alanları doldurunuz.',
    enterEmailForReset: 'Lütfen şifre sıfırlama için e-posta adresinizi giriniz.',
    passwordResetSent: 'Şifre sıfırlama e-postası gönderildi.\nLütfen e-posta adresinizi kontrol edin.',
    passwordResetFailed: 'Şifre sıfırlama başarısız: ',
    loggingIn: 'Giriş yapılıyor...',
    creatingAccount: 'Hesap oluşturuluyor...',
    iAccept: 'Kabul ediyorum',
    and: 've',
    selectAvatar: 'Avatar Seç',
    usernamePlaceholder: 'sadece küçük harf, rakam, _ ve .',
    usernameChangeLimit: '15 gün içinde en fazla 2 defa kullanıcı adı değiştirebilirsiniz.',
    bioPlaceholder: 'Kendiniz hakkında kısa bir açıklama...',
    male: 'Erkek',
    female: 'Kadın',
    other: 'Diğer',
    gender: 'Cinsiyet',
    acceptTermsAndPrivacy: 'Lütfen kullanıcı sözleşmesi ve gizlilik politikasını kabul edin.',

    // Giriş/Kayıt
    login: 'Giriş Yap',
    register: 'Kayıt Ol',
    email: 'E-posta',
    password: 'Şifre',
    passwordMinChars: 'Şifre (En az 8 karakter)',
    forgotPassword: 'Şifremi Unuttum',
    username: 'Kullanıcı Adı',
    noAccount: 'Hesabınız yok mu? Kayıt Olun',
    haveAccount: 'Zaten hesabım var, Giriş Yapın',

    // Ana Sayfa
    home: 'Ana Sayfa',
    forYou: 'Sizin için',
    following: 'Takip edilenler',

    // Profil
    profile: 'Profil',
    editProfile: 'Profili Düzenle',
    followers: 'Takipçiler',
    following: 'Takip Edilenler',
    posts: 'Gönderiler',

    // Gönderiler
    post: 'Gönderi',
    addPost: 'Gönderi Paylaş',
    deletePost: 'Gönderiyi Sil',
    reportPost: 'Gönderiyi Bildir',
    likedBy: 'Beğenenler',
    comments: 'Yorumlar',
    addComment: 'Yorum Ekle',
    writeYourPost: 'Gönderinizi buraya yazın...',
    postRules: 'Uyarı: Hakaret, kütür, nefret söylemi ve uygunsuz içerikler yasaktır. Bu tür içerikler kaldırılabilir ve hesabınız engellenebilir.',
    lyrics: 'Şarkı Sözü',
    poem: 'Şiir',
    pleaseEnterContent: 'Lütfen gönderi içeriğinizi yazınız.',
    characterLimitExceeded: 'Gönderi içeriği {limit} karakteri aşamaz.',
    readMore: 'Devamını Oku',
    readLess: 'Daha Az Göster',
    save: 'Kaydet',
    saved: 'Kaydedildi',
    close: 'Kapat',
    share: 'Paylaş',
    like: 'Beğen',
    unlike: 'Beğenmekten Vazgeç',
    comment: 'Yorum Yap',
    follow: 'Takip Et',
    unfollow: 'Takibi Bırak',
    block: 'Engelle',
    unblock: 'Engeli Kaldır',
    report: 'Bildir',
    delete: 'Sil',
    cancel: 'İptal',
    confirm: 'Onayla',
    search: 'Ara',
    searchUsers: 'Kullanıcı Ara',
    searchPosts: 'Gönderi Ara',
    noResults: 'Sonuç bulunamadı',
    searchResults: 'Arama Sonuçları',
    loading: 'Yükleniyor...',
    loadMore: 'Daha Fazla Yükle',
    refresh: 'Yenile',

    // Liderlik Tablosu
    leaderboard: 'Liderlik Tablosu',
    allTimeMostPopular: 'Tüm Zamanların En Ünlüleri',
    dailyStars: 'Günün Yıldızları',
    hourlyTrends: 'Saatin Trendleri',
    mostFollowed: 'En Çok Takip Edilenler',

    // Ayarlar
    settings: 'Ayarlar',
    account: 'Hesap',
    privacy: 'Gizlilik',
    notifications: 'Bildirimler',
    language: 'Dil',
    theme: 'Tema',
    darkMode: 'Koyu Tema',
    lightMode: 'Açık Tema',
    blockedUsers: 'Engellenen Kullanıcılar',
    logout: 'Çıkış Yap',
    generalSettings: 'Genel Ayarlar',
    privateAccount: 'Gizli Hesap',
    savedContent: 'Kaydedilen İçerikler',
    help: 'Yardım',
    about: 'Hakkında',
    version: 'Versiyon',

    // Bildirimler
    newNotification: 'Yeni Bildirim',
    likedYourPost: 'gönderinizi beğendi',
    commentedOnYourPost: 'gönderinize yorum yaptı',
    startedFollowingYou: 'sizi takip etmeye başladı',
    mentionedYou: 'sizi bir yorumda etiketledi',
    repliedToYourComment: 'yorumunuza cevap verdi',

    // Gönderi Seçenekleri
    postOptions: 'Gönderi Seçenekleri',
    whatWouldYouLikeToDo: 'Ne yapmak istersiniz?',
    confirmDeletePost: 'Bu gönderiyi tamamen silmek istediğinize emin misiniz?',
    error: 'Hata',
    followError: 'Takip işlemi sırasında bir hata oluştu.',
    userNotFound: 'Kullanıcı bulunamadı.',
    user: 'Kullanıcı',

    // Kayıtlı İçerikler
    savedPosts: 'Kaydedilen Gönderiler',
    likedPosts: 'Beğenilen Gönderiler',
    commentedPosts: 'Yorum Yapılan Gönderiler',
    noSavedPosts: 'Kaydedilen gönderi bulunmamaktadır',
    noLikedPosts: 'Beğenilen gönderi bulunmamaktadır',
    noCommentedPosts: 'Yorum yapılan gönderi bulunmamaktadır',

    // Hata Mesajları
    somethingWentWrong: 'Bir şeyler yanlış gitti',
    pleaseLoginAgain: 'Lütfen tekrar giriş yapın',
    connectionError: 'Bağlantı hatası',
    likedPostsError: 'Beğenilen gönderiler yüklenirken bir hata oluştu.',
    commentedPostsError: 'Yorum yapılan gönderiler yüklenirken bir hata oluştu.',
    errorLoadingPosts: 'Gönderiler yüklenirken bir hata oluştu.',
    errorLoadingSavedPosts: 'Kaydedilen gönderiler yüklenirken bir hata oluştu.',
    likeError: 'Beğeni işlemi sırasında bir hata oluştu.',
    postSaved: 'Gönderi kaydedildi.',
    postRemovedFromSaved: 'Gönderi kayıtlardan kaldırıldı.',
    errorSavingPost: 'Gönderi kaydedilirken bir hata oluştu.',
    errorDeletingPost: 'Gönderi silinirken bir hata oluştu.',
    postReported: 'Gönderi bildirildi. İnceleme sonrası gerekli işlemler yapılacaktır.',

    // Diğer
    termsOfService: 'Kullanıcı Sözleşmesi',
    privacyPolicy: 'Gizlilik Politikası',
    help: 'Yardım',
    about: 'Hakkında',
    version: 'Sürüm',
  },
  en: {
    // General
    appName: 'VELMORA',
    loading: 'Loading...',
    save: 'Save',
    cancel: 'Cancel',
    delete: 'Delete',
    edit: 'Edit',
    close: 'Close',
    confirm: 'Confirm',
    yes: 'Yes',
    no: 'No',
    ok: 'OK',
    error: 'Error',
    success: 'Success',
    warning: 'Warning',
    info: 'Info',
    welcome: 'Welcome',
    joinUs: 'Join Us',
    enterEmail: 'Please enter your email address.',
    enterUsername: 'Please enter your username.',
    invalidEmail: 'Invalid email address.',
    fillAllFields: 'Please fill in all fields.',
    enterEmailForReset: 'Please enter your email address for password reset.',
    passwordResetSent: 'Password reset email sent.\nPlease check your email.',
    passwordResetFailed: 'Password reset failed: ',
    loggingIn: 'Logging in...',
    creatingAccount: 'Creating account...',
    iAccept: 'I accept',
    and: 'and',
    selectAvatar: 'Select Avatar',
    usernamePlaceholder: 'lowercase letters, numbers, _ and . only',
    usernameChangeLimit: 'You can change your username at most 2 times within 15 days.',
    bioPlaceholder: 'A short description about yourself...',
    male: 'Male',
    female: 'Female',
    other: 'Other',
    gender: 'Gender',
    acceptTermsAndPrivacy: 'Please accept the Terms of Service and Privacy Policy.',

    // Login/Register
    login: 'Login',
    register: 'Register',
    email: 'Email',
    password: 'Password',
    passwordMinChars: 'Password (Min 8 characters)',
    forgotPassword: 'Forgot Password',
    username: 'Username',
    noAccount: 'Don\'t have an account? Register',
    haveAccount: 'Already have an account? Login',

    // Home
    home: 'Home',
    forYou: 'For You',
    following: 'Following',

    // Profile
    profile: 'Profile',
    editProfile: 'Edit Profile',
    followers: 'Followers',
    following: 'Following',
    posts: 'Posts',

    // Posts
    post: 'Post',
    addPost: 'Share Post',
    deletePost: 'Delete Post',
    reportPost: 'Report Post',
    likedBy: 'Liked By',
    comments: 'Comments',
    addComment: 'Add Comment',
    writeYourPost: 'Write your post here...',
    postRules: 'Warning: Harassment, profanity, hate speech, and inappropriate content are prohibited. Such content may be removed and your account may be blocked.',
    lyrics: 'Lyrics',
    poem: 'Poem',
    pleaseEnterContent: 'Please enter your post content.',
    characterLimitExceeded: 'Post content cannot exceed {limit} characters.',
    readMore: 'Read More',
    readLess: 'Read Less',
    save: 'Save',
    saved: 'Saved',
    close: 'Close',
    share: 'Share',
    like: 'Like',
    unlike: 'Unlike',
    comment: 'Comment',
    follow: 'Follow',
    unfollow: 'Unfollow',
    block: 'Block',
    unblock: 'Unblock',
    report: 'Report',
    delete: 'Delete',
    cancel: 'Cancel',
    confirm: 'Confirm',
    search: 'Search',
    searchUsers: 'Search Users',
    searchPosts: 'Search Posts',
    noResults: 'No results found',
    searchResults: 'Search Results',
    loading: 'Loading...',
    loadMore: 'Load More',
    refresh: 'Refresh',

    // Leaderboard
    leaderboard: 'Leaderboard',
    allTimeMostPopular: 'All-Time Most Popular',
    dailyStars: 'Daily Stars',
    hourlyTrends: 'Hourly Trends',
    mostFollowed: 'Most Followed',

    // Settings
    settings: 'Settings',
    account: 'Account',
    privacy: 'Privacy',
    notifications: 'Notifications',
    language: 'Language',
    theme: 'Theme',
    darkMode: 'Dark Mode',
    lightMode: 'Light Mode',
    blockedUsers: 'Blocked Users',
    logout: 'Logout',
    generalSettings: 'General Settings',
    privateAccount: 'Private Account',
    savedContent: 'Saved Content',
    help: 'Help',
    about: 'About',
    version: 'Version',

    // Notifications
    newNotification: 'New Notification',
    likedYourPost: 'liked your post',
    commentedOnYourPost: 'commented on your post',
    startedFollowingYou: 'started following you',
    mentionedYou: 'mentioned you in a comment',
    repliedToYourComment: 'replied to your comment',

    // Post Options
    postOptions: 'Post Options',
    whatWouldYouLikeToDo: 'What would you like to do?',
    confirmDeletePost: 'Are you sure you want to completely delete this post?',
    error: 'Error',
    followError: 'An error occurred during the follow operation.',
    userNotFound: 'User not found.',
    user: 'User',

    // Saved Content
    savedPosts: 'Saved Posts',
    likedPosts: 'Liked Posts',
    commentedPosts: 'Commented Posts',
    noSavedPosts: 'No saved posts found',
    noLikedPosts: 'No liked posts found',
    noCommentedPosts: 'No commented posts found',

    // Error Messages
    somethingWentWrong: 'Something went wrong',
    pleaseLoginAgain: 'Please login again',
    connectionError: 'Connection error',
    likedPostsError: 'An error occurred while loading liked posts.',
    commentedPostsError: 'An error occurred while loading commented posts.',
    errorLoadingPosts: 'An error occurred while loading posts.',
    errorLoadingSavedPosts: 'An error occurred while loading saved posts.',
    likeError: 'An error occurred during the like operation.',
    postSaved: 'Post saved successfully.',
    postRemovedFromSaved: 'Post removed from saved.',
    errorSavingPost: 'An error occurred while saving the post.',
    errorDeletingPost: 'An error occurred while deleting the post.',
    postReported: 'Post reported. Appropriate actions will be taken after review.',

    // Other
    termsOfService: 'Terms of Service',
    privacyPolicy: 'Privacy Policy',
    help: 'Help',
    about: 'About',
    version: 'Version',
  }
};

// Context oluştur
const LanguageContext = createContext();

// Context provider
export const LanguageProvider = ({ children }) => {
  // Get device language
  const getDeviceLanguage = () => {
    // We can use Platform API to get device language
    // For simplicity, we only support 'tr' and 'en'
    // In a real app, we would use a more complex logic
    try {
      // Always return 'tr' as default language as requested by user
      return 'tr';
    } catch (error) {
      console.error('Error getting device language:', error);
      return 'tr'; // Default to Turkish in case of error
    }
  };

  const [language, setLanguage] = useState(getDeviceLanguage()); // Cihaz diline göre varsayılan dil
  const [translations, setTranslations] = useState(translationData[getDeviceLanguage()] || translationData.tr);
  const [isLoading, setIsLoading] = useState(true);

  // Dil tercihini AsyncStorage'dan yükle
  useEffect(() => {
    const loadLanguagePreference = async () => {
      try {
        const savedLanguage = await AsyncStorage.getItem('languagePreference');

        if (savedLanguage !== null) {
          setLanguage(savedLanguage);
          setTranslations(translationData[savedLanguage] || translationData.tr);
        }

        // Kullanıcı oturum açmışsa Firestore'dan dil tercihini al
        if (auth.currentUser) {
          const userRef = doc(db, 'users', auth.currentUser.uid);
          const userDoc = await getDoc(userRef);

          if (userDoc.exists() && userDoc.data().languagePreference) {
            const firestoreLanguage = userDoc.data().languagePreference;
            setLanguage(firestoreLanguage);
            setTranslations(translationData[firestoreLanguage] || translationData.tr);
          }
        }
      } catch (error) {
        console.error('Dil tercihi yüklenirken hata:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadLanguagePreference();
  }, []);

  // Dil değiştirme fonksiyonu
  const changeLanguage = async (lang) => {
    try {
      if (!translationData[lang]) {
        console.error(`Dil bulunamadı: ${lang}`);
        return;
      }

      console.log('Dil değiştiriliyor:', lang);

      setLanguage(lang);
      setTranslations(translationData[lang]);

      // AsyncStorage'a kaydet
      await AsyncStorage.setItem('languagePreference', lang);

      // Kullanıcı oturum açmışsa Firestore'a kaydet
      if (auth.currentUser) {
        const userRef = doc(db, 'users', auth.currentUser.uid);
        await updateDoc(userRef, {
          languagePreference: lang
        });
      }
    } catch (error) {
      console.error('Dil tercihi kaydedilirken hata:', error);
    }
  };

  return (
    <LanguageContext.Provider value={{ language, translations, changeLanguage, isLoading }}>
      {children}
    </LanguageContext.Provider>
  );
};

// Custom hook
export const useLanguage = () => useContext(LanguageContext);
