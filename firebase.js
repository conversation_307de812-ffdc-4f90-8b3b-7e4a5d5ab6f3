// firebase.js

import { initializeApp, getApps, getApp } from "firebase/app";
import {
  initializeAuth,
  getReactNativePersistence,
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  sendPasswordResetEmail,
  sendEmailVerification,
  signOut,
  updateProfile
} from "firebase/auth";
import ReactNativeAsyncStorage from "@react-native-async-storage/async-storage";
import {
  getFirestore,
  doc,
  setDoc,
  getDoc,
  collection,
  query,
  where,
  getDocs
} from "firebase/firestore";

/**
 * Firebase Konfigürasyonu
 */
const firebaseConfig = {
  apiKey: "AIzaSyCtGo3a9bMb-CnA2Ib04i8ZNXtLl1GI_tA",
  authDomain: "velmora-65393.firebaseapp.com",
  projectId: "velmora-65393",
  storageBucket: "velmora-65393.appspot.com",
  messagingSenderId: "391991822918",
  appId: "1:391991822918:web:34bd05102e1cba8be59847",
};

/**
 * Uygulamayı başlat veya zaten başlatılmışsa mevcut instance'ı al
 */
let app;
if (!getApps().length) {
  app = initializeApp(firebaseConfig);
} else {
  app = getApp();
}

/**
 * React Native için oturum bilgisini AsyncStorage üzerinden kalıcı tut
 */
const auth = initializeAuth(app, {
  persistence: getReactNativePersistence(ReactNativeAsyncStorage),
});

/**
 * Firestore (veritabanı) başlat
 */
const db = getFirestore(app);

/**
 * KULLANICI GİRİŞ FONKSİYONU
 */
export const loginUser = async (email, password) => {
  try {
    const userCredential = await signInWithEmailAndPassword(auth, email, password);
    const user = userCredential.user;
    if (!user.emailVerified) {
      // E-posta doğrulaması yoksa hata fırlat
      throw new Error("Lütfen e-postanızı doğrulayın.");
    }
    return user;
  } catch (error) {
    switch (error.code) {
      case "auth/user-not-found":
        throw new Error("Bu e-posta adresine ait bir hesap bulunamadı.");
      case "auth/wrong-password":
        throw new Error("Yanlış şifre girdiniz.");
      case "auth/invalid-email":
        throw new Error("Geçersiz e-posta adresi.");
      default:
        throw new Error("Giriş başarısız: " + error.message);
    }
  }
};

/**
 * KULLANICI KAYIT FONKSİYONU
 *  => username eşsizliği eklendi
 */
export const registerUser = async (email, password, username) => {
  try {
    // (Opsiyonel) Kullanıcı adı regex (harf, sayı, alt çizgi, nokta)
    const usernameRegex = /^[a-zA-Z0-9_\.]+$/;
    if (!usernameRegex.test(username)) {
      throw new Error("Kullanıcı adı sadece harf, rakam, alt çizgi ve nokta içerebilir.");
    }

    // 1) 'users' koleksiyonunda bu username kullanılıyor mu?
    const q = query(collection(db, "users"), where("username", "==", username));
    const existingUserDocs = await getDocs(q);
    if (!existingUserDocs.empty) {
      throw new Error("Bu kullanıcı adı zaten kullanımda.");
    }

    // 2) Firebase Auth üzerinde kullanıcı oluştur
    const userCredential = await createUserWithEmailAndPassword(auth, email, password);
    const user = userCredential.user;

    // 3) Auth içinde displayName'i güncelle
    await updateProfile(user, { displayName: username });

    // 4) Firestore'da "users/{uid}" dokümanına kaydet
    await setDoc(doc(db, "users", user.uid), {
      username: username,
      email: email,
      uid: user.uid,
      createdAt: new Date(),
      followers: [],
      following: [],
    });

    // 5) E-posta doğrulaması gönder
    await sendEmailVerification(user);

    return user;
  } catch (error) {
    switch (error.code) {
      case "auth/email-already-in-use":
        throw new Error("Bu e-posta adresi zaten kullanılıyor.");
      case "auth/weak-password":
        throw new Error("Şifreniz en az 6 karakter olmalıdır.");
      case "auth/invalid-email":
        throw new Error("Geçersiz e-posta adresi.");
      default:
        throw new Error("Kayıt başarısız: " + error.message);
    }
  }
};

/**
 * ŞİFRE SIFIRLAMA FONKSİYONU
 */
export const resetPassword = async (email) => {
  try {
    await sendPasswordResetEmail(auth, email);
  } catch (error) {
    switch (error.code) {
      case "auth/user-not-found":
        throw new Error("Bu e-posta adresine ait bir hesap bulunamadı.");
      case "auth/invalid-email":
        throw new Error("Geçersiz e-posta adresi.");
      default:
        throw new Error("Şifre sıfırlama başarısız: " + error.message);
    }
  }
};

/**
 * KULLANICI ÇIKIŞ
 */
export const logoutUser = async () => {
  try {
    await signOut(auth);
  } catch (error) {
    // Hata verse de ekranda gösterme istersen, burayı bos birak
    // throw new Error("Çıkış yaparken hata oluştu: " + error.message);
  }
};

/**
 * KULLANICI PROFİL BİLGİSİ GETİRME (Firestore 'users' koleksiyonundan)
 */
export const getUserProfile = async (uid) => {
  try {
    const userDoc = await getDoc(doc(db, "users", uid));
    if (userDoc.exists()) {
      return userDoc.data();
    }
    return null;
  } catch (error) {
    throw new Error("Profil getirme hatası: " + error.message);
  }
};

/**
 * auth ve db export
 */
export { auth, db };
