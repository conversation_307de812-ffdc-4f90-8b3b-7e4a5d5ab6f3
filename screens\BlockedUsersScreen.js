import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  FlatList,
  StyleSheet,
  TouchableOpacity,
  Image,
  ActivityIndicator,
  RefreshControl,
  ToastAndroid
} from 'react-native';
import { doc, getDoc, updateDoc, arrayRemove } from 'firebase/firestore';
import { db, auth } from '../firebase';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';

// Avatar mapping
const avatarMap = {
  avatar1: require('../assets/avatar1.png'),
  avatar2: require('../assets/avatar2.png'),
  avatar3: require('../assets/avatar3.png'),
  avatar4: require('../assets/avatar4.png'),
  avatar5: require('../assets/avatar5.png'),
  avatar6: require('../assets/avatar6.png'),
  avatar7: require('../assets/avatar7.png'),
  avatar8: require('../assets/avatar8.png'),
  avatar9: require('../assets/avatar9.png'),
  avatar10: require('../assets/avatar10.png'),
  avatar11: require('../assets/avatar11.png'),
  avatar12: require('../assets/avatar12.png'),
  avatar13: require('../assets/avatar13.png'),
  avatar14: require('../assets/avatar14.png'),
};

export default function BlockedUsersScreen() {
  const [blockedUsers, setBlockedUsers] = useState([]);
  const [userProfiles, setUserProfiles] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  const navigation = useNavigation();
  const currentUser = auth.currentUser;

  // Engellenen kullanıcıları yükle
  useEffect(() => {
    if (!currentUser) return;

    loadBlockedUsers();
  }, [currentUser]);

  // Engellenen kullanıcıları yükle
  const loadBlockedUsers = async () => {
    if (!currentUser) return;

    setLoading(true);
    try {
      // Kullanıcı bilgilerini al
      const userRef = doc(db, 'users', currentUser.uid);
      const userDoc = await getDoc(userRef);

      if (!userDoc.exists()) {
        setLoading(false);
        return;
      }

      const userData = userDoc.data();
      const blockedList = userData.blockedUsers || [];
      setBlockedUsers(blockedList);

      // Engellenen kullanıcıların profillerini yükle
      if (blockedList.length > 0) {
        const profiles = [];

        for (const userId of blockedList) {
          const blockedUserRef = doc(db, 'users', userId);
          const blockedUserDoc = await getDoc(blockedUserRef);

          if (blockedUserDoc.exists()) {
            profiles.push({
              id: userId,
              ...blockedUserDoc.data()
            });
          }
        }

        setUserProfiles(profiles);
      } else {
        setUserProfiles([]);
      }
    } catch (error) {
      console.error('Engellenen kullanıcıları yükleme hatası:', error);
      Alert.alert('Hata', 'Engellenen kullanıcılar yüklenirken bir hata oluştu.');
    }

    setLoading(false);
  };

  // Yenileme işlemi
  const onRefresh = () => {
    setRefreshing(true);
    loadBlockedUsers().then(() => setRefreshing(false));
  };

  // Engeli kaldır
  const handleUnblock = async (userId) => {
    if (!currentUser || !userId) return;

    try {
      // Kullanıcıyı engellenen listesinden çıkar
      const userRef = doc(db, 'users', currentUser.uid);
      await updateDoc(userRef, {
        blockedUsers: arrayRemove(userId)
      });

      // Yerel state'i güncelle
      setBlockedUsers(prev => prev.filter(id => id !== userId));
      setUserProfiles(prev => prev.filter(user => user.id !== userId));

      ToastAndroid.show('Kullanıcının engeli kaldırıldı.', ToastAndroid.SHORT);
    } catch (error) {
      console.error('Engel kaldırma hatası:', error);
      ToastAndroid.show('Kullanıcının engeli kaldırılırken bir hata oluştu.', ToastAndroid.SHORT);
    }
  };

  // Kullanıcı öğesi render
  const renderUserItem = ({ item }) => {
    const profilePic = item.profilePic || 'avatar1';

    return (
      <View style={styles.userItem}>
        <View style={styles.userInfo}>
          <Image
            source={avatarMap[profilePic] || require('../assets/default-avatar.png')}
            style={styles.avatar}
          />
          <Text style={styles.username}>{item.username || 'Kullanıcı'}</Text>
        </View>

        <TouchableOpacity
          style={styles.unblockButton}
          onPress={() => handleUnblock(item.id)}
        >
          <Text style={styles.unblockButtonText}>Engeli Kaldır</Text>
        </TouchableOpacity>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      {/* Üst Bar */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Engellenen Kullanıcılar</Text>
        <View style={styles.rightSpace} />
      </View>

      {loading ? (
        <ActivityIndicator size="large" color="#8e44ad" style={styles.loader} />
      ) : userProfiles.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Ionicons name="shield-checkmark-outline" size={64} color="#555" />
          <Text style={styles.emptyText}>Engellediğiniz kullanıcı bulunmamaktadır</Text>
        </View>
      ) : (
        <FlatList
          data={userProfiles}
          renderItem={renderUserItem}
          keyExtractor={item => item.id}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              tintColor="#fff"
            />
          }
          contentContainerStyle={styles.listContainer}
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
    paddingTop: 30
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 15,
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#333'
  },
  backButton: {
    padding: 5
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff'
  },
  rightSpace: {
    width: 24
  },
  loader: {
    marginTop: 20
  },
  listContainer: {
    paddingBottom: 20
  },
  userItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#222'
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 10,
    borderWidth: 1,
    borderColor: '#0066CC'
  },
  username: {
    fontSize: 16,
    color: '#fff',
    fontWeight: 'bold'
  },
  unblockButton: {
    backgroundColor: '#e74c3c',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 4
  },
  unblockButtonText: {
    color: '#fff',
    fontSize: 14
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20
  },
  emptyText: {
    color: '#777',
    fontSize: 16,
    marginTop: 10,
    textAlign: 'center'
  }
});
