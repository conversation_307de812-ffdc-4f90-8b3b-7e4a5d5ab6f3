import React from 'react';
import { View } from 'react-native';
import Svg, { Path, Circle, G, LinearGradient, Stop, Defs, Text as SvgText } from 'react-native-svg';

const VelmoraLogo = ({ width = 200, height = 200 }) => {
  return (
    <View style={{ width, height }}>
      <Svg width={width} height={height} viewBox="0 0 200 200">
        <Defs>
          <LinearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
            <Stop offset="0%" stopColor="#0066CC" stopOpacity="1" />
            <Stop offset="100%" stopColor="#1DA1F2" stopOpacity="1" />
          </LinearGradient>
        </Defs>
        
        {/* Dış Çember */}
        <Circle cx="100" cy="100" r="90" fill="none" stroke="url(#grad)" strokeWidth="4" />
        
        {/* <PERSON><PERSON>ember */}
        <Circle cx="100" cy="100" r="70" fill="none" stroke="url(#grad)" strokeWidth="2" opacity="0.7" />
        
        {/* V Harfi */}
        <Path
          d="M70,60 L100,140 L130,60"
          fill="none"
          stroke="url(#grad)"
          strokeWidth="8"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        
        {/* Müzik Notası */}
        <G transform="translate(130, 100) scale(0.8)">
          <Path
            d="M0,0 C5,0 10,-5 10,-15 C10,-25 5,-30 0,-30 C-5,-30 -10,-25 -10,-15 C-10,-5 -5,0 0,0 Z"
            fill="url(#grad)"
          />
          <Path
            d="M0,-30 L0,-80"
            stroke="url(#grad)"
            strokeWidth="4"
            strokeLinecap="round"
          />
        </G>
        
        {/* Şiir Sembolü (Kalem) */}
        <G transform="translate(70, 100) scale(0.8)">
          <Path
            d="M-10,15 L10,-15 L5,-20 L-15,10 Z"
            fill="url(#grad)"
          />
          <Path
            d="M-15,10 L-10,15"
            stroke="url(#grad)"
            strokeWidth="2"
          />
        </G>
      </Svg>
    </View>
  );
};

export default VelmoraLogo;
