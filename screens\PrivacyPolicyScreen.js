// screens/PrivacyPolicyScreen.js
import React from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';

const PrivacyPolicyScreen = () => {
  const navigation = useNavigation();

  return (
    <View style={styles.container}>
      {/* Üst Başlık */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>VELMORA Gizlilik Politikası</Text>
      </View>

      <ScrollView style={styles.content} contentContainerStyle={styles.contentContainer}>
        <Text style={styles.lastUpdate}>Son Güncelleme: 13 Nisan 2025</Text>

        <Text style={styles.sectionHeader}>1. <PERSON><PERSON> Bilgiler</Text>
        <Text style={styles.paragraph}>
          VELMORA uygulamasını kullanarak, bu Gizlilik Politikası'nda belirtilen koşulları kabul etmiş sayılırsınız.
          Kullanıcılarımızın kişisel verilerinin güvenliği bizim için önemlidir. Bu politika, hangi bilgileri topladığımızı,
          bu bilgileri nasıl kullandığımızı ve nasıl koruduğumuzu açıklar.
        </Text>

        <Text style={styles.sectionHeader}>2. Toplanan Bilgiler</Text>
        <Text style={styles.paragraph}>
          - <Text style={styles.bold}>Hesap Bilgileri:</Text> E-posta adresi, kullanıcı adı, şifre (şifreler şifrelenerek saklanır).{"\n"}
          - <Text style={styles.bold}>Profil Bilgileri:</Text> Kullanıcı adı, biyografi, cinsiyet, avatar resmi.{"\n"}
          - <Text style={styles.bold}>Gönderiler ve Etkileşimler:</Text> Kullanıcıların paylaştığı gönderiler, yorumlar, beğeniler ve takip edilen kullanıcılar.{"\n"}
          - <Text style={styles.bold}>İletişim Bilgileri:</Text> Destek ve yardım talepleri sırasında kullanıcı tarafından sağlanan bilgiler.{"\n"}
          - <Text style={styles.bold}>Cihaz ve Kullanım Verileri:</Text> IP adresleri, cihaz türü, işletim sistemi ve uygulama kullanım istatistikleri.
        </Text>

        <Text style={styles.sectionHeader}>3. Bilgilerin Kullanımı</Text>
        <Text style={styles.paragraph}>
          Toplanan bilgiler aşağıdaki amaçlarla kullanılır:{"\n"}
          • Kullanıcı hesaplarının yönetimi ve doğrulaması.{"\n"}
          • Hizmetlerimizin kişiselleştirilmesi.{"\n"}
          • Kullanıcı taleplerinin işlenmesi ve destek sunulması.{"\n"}
          • Uygulama içi etkileşimlerin ve içeriklerin yönetimi.{"\n"}
          • Kullanıcı deneyimini iyileştirmek için analizler gerçekleştirme.{"\n"}
          • Kullanıcılarımızı güncelleme ve yeniliklerden haberdar etme.
        </Text>

        <Text style={styles.sectionHeader}>4. Bilgi Paylaşımı</Text>
        <Text style={styles.paragraph}>
          Kişisel bilgileriniz, açık izniniz olmadan üçüncü kişilerle paylaşılmaz. Ancak aşağıdaki durumlarda bilgiler paylaşılabilir:{"\n"}
          • Yasaların gerektirdiği durumlar.{"\n"}
          • Yasal işlemlerde şirket haklarının korunması.{"\n"}
          • Hizmet sağlayıcılarımıza, teknik altyapıyı sağlamak veya kullanıcı destek hizmetlerini yürütmek amacıyla.
        </Text>

        <Text style={styles.sectionHeader}>5. Kullanıcı Hakları</Text>
        <Text style={styles.paragraph}>
          Kullanıcılar kişisel bilgilerine erişme, düzeltme ve silme haklarına sahiptir. Kullanıcılar ayrıca uygulama içindeki ayarlar bölümünden
          hesap bilgilerini güncelleyebilir veya hesaplarını silebilir.
        </Text>

        <Text style={styles.sectionHeader}>6. Veri Güvenliği</Text>
        <Text style={styles.paragraph}>
          Kullanıcı verilerinin güvenliğini sağlamak için gerekli teknik ve idari önlemleri alıyoruz. Kullanıcı hesaplarına erişim şifrelerle korunur
          ve hassas bilgiler şifrelenerek saklanır.
        </Text>

        <Text style={styles.sectionHeader}>7. Çerezler ve İzleme Teknolojileri</Text>
        <Text style={styles.paragraph}>
          VELMORA, kullanıcı deneyimini geliştirmek için çerezler ve benzeri teknolojiler kullanabilir. Kullanıcılar, cihazlarındaki ayarlardan çerez
          kullanımını kontrol edebilir veya engelleyebilir.
        </Text>

        <Text style={styles.sectionHeader}>8. Üçüncü Taraf Bağlantılar</Text>
        <Text style={styles.paragraph}>
          VELMORA, üçüncü taraf web sitelerine veya uygulamalara bağlantılar içerebilir. Bu üçüncü tarafların gizlilik uygulamalarından sorumlu olmadığımızı
          belirtir, kullanıcıları bu sitelerin gizlilik politikalarını okumaya teşvik ederiz.
        </Text>

        <Text style={styles.sectionHeader}>9. Çocukların Gizliliği</Text>
        <Text style={styles.paragraph}>
          Uygulamamız, 13 yaşın altındaki çocuklar için tasarlanmamıştır ve bu yaştaki çocuklardan bilerek kişisel veri toplamaz.
          13 yaş altındaki bir çocuğun verilerini topladığımızı fark edersek, bu bilgileri derhal sileriz.
        </Text>

        <Text style={styles.sectionHeader}>10. Gizlilik Politikasında Değişiklikler</Text>
        <Text style={styles.paragraph}>
          Bu politikada zaman zaman değişiklik yapılabilir. Yapılan önemli değişikliklerde kullanıcılarımızı uygulama içinde veya e-posta yoluyla bilgilendireceğiz.
          Kullanıcıların düzenli olarak bu sayfayı kontrol etmeleri tavsiye edilir.
        </Text>

        <Text style={styles.sectionHeader}>11. İletişim</Text>
        <Text style={styles.paragraph}>
          Gizlilik politikamız ile ilgili herhangi bir sorunuz varsa, lütfen bizimle şu adresten iletişime geçiniz:{"\n"}
          E-posta: <EMAIL>{"\n"}
          Adres: VELMORA Teknoloji Hizmetleri, İstanbul, Türkiye{"\n\n"}
          VELMORA uygulamasını kullanarak bu gizlilik politikasını kabul etmiş sayılırsınız.
        </Text>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#000' },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    height: 60,
    borderBottomWidth: 1,
    borderBottomColor: '#333',
    paddingHorizontal: 10,
    backgroundColor: '#000',
  },
  backButton: { padding: 5 },
  headerTitle: {
    flex: 1,
    textAlign: 'center',
    color: '#fff',
    fontSize: 20,
    fontWeight: 'bold',
  },
  content: { paddingHorizontal: 20 },
  contentContainer: { paddingVertical: 20 },
  lastUpdate: {
    color: '#fff',
    fontSize: 14,
    marginBottom: 20,
    textAlign: 'center',
  },
  sectionHeader: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 20,
    marginBottom: 10,
  },
  paragraph: {
    color: '#fff',
    fontSize: 16,
    lineHeight: 24,
    textAlign: 'justify',
    marginBottom: 15,
  },
  bold: { fontWeight: 'bold' },
});

export default PrivacyPolicyScreen;
