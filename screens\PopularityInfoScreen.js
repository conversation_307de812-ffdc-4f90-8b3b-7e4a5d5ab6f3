import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';

const PopularityInfoScreen = () => {
  const navigation = useNavigation();

  const rankInfo = [
    { rank: 'Efsane', threshold: 1000, color: '#FFD700', description: 'En yüksek popülerlik seviyesi. Velmora\'nın en tanınmış ve saygın üyeleri.' },
    { rank: 'Yıldız', threshold: 500, color: '#E6E6FA', description: 'Çok yüksek popülerlik seviyesi. Platformda öne çıkan ve takip edilen kullanıcılar.' },
    { rank: 'Popüler', threshold: 200, color: '#FF6B6B', description: 'Yüksek popülerlik seviyesi. İçerikleri beğ<PERSON>len ve takip edilen kullanıcılar.' },
    { rank: '<PERSON><PERSON><PERSON><PERSON><PERSON>', threshold: 100, color: '#4A90E2', description: 'Orta-yüksek popülerlik seviyesi. Popülerliği artmakta olan kullanıcılar.' },
    { rank: 'Aktif', threshold: 50, color: '#50C878', description: 'Orta popülerlik seviyesi. Düzenli içerik paylaşan ve etkileşim alan kullanıcılar.' },
    { rank: 'Başlangıç', threshold: 20, color: '#9370DB', description: 'Düşük-orta popülerlik seviyesi. Platformda aktif olmaya başlayan kullanıcılar.' },
    { rank: 'Yeni Üye', threshold: 0, color: '#A9A9A9', description: 'Başlangıç seviyesi. Platforma yeni katılan veya henüz yeterli etkileşim almamış kullanıcılar.' },
  ];

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Popülerlik ve Ünvanlar</Text>
      </View>

      <ScrollView style={styles.scrollView}>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Popülerlik Nedir?</Text>
          <Text style={styles.sectionText}>
            Popülerlik, VELMORA platformunda kullanıcıların etkileşimlerini ve platformdaki etkisini ölçen bir puanlama sistemidir. 
            Popülerlik puanınız, aldığınız beğeniler, yorumlar ve takipçi sayınıza göre hesaplanır.
          </Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Popülerlik Nasıl Hesaplanır?</Text>
          <View style={styles.formulaContainer}>
            <View style={styles.formulaItem}>
              <Ionicons name="heart" size={24} color="#e74c3c" />
              <Text style={styles.formulaText}>10 beğeni = 1 popülerlik puanı</Text>
            </View>
            <View style={styles.formulaItem}>
              <Ionicons name="chatbubble" size={24} color="#3498db" />
              <Text style={styles.formulaText}>3 yorum = 1 popülerlik puanı</Text>
            </View>
            <View style={styles.formulaItem}>
              <Ionicons name="person-add" size={24} color="#2ecc71" />
              <Text style={styles.formulaText}>1 takipçi = 1 popülerlik puanı</Text>
            </View>
          </View>
          <Text style={styles.noteText}>
            Not: Popülerlik puanınız otomatik olarak hesaplanır ve profilinizde gösterilir.
          </Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Ünvan Seviyeleri</Text>
          <Text style={styles.sectionText}>
            Popülerlik puanınıza göre size bir ünvan verilir. Bu ünvan, profilinizde kullanıcı adınızın yanında gösterilir.
          </Text>

          {rankInfo.map((rank, index) => (
            <View key={index} style={styles.rankItem}>
              <View style={[styles.rankBadge, { backgroundColor: rank.color }]}>
                <Text style={styles.rankBadgeText}>{rank.rank}</Text>
              </View>
              <View style={styles.rankInfo}>
                <Text style={styles.rankThreshold}>{rank.threshold}+ popülerlik puanı</Text>
                <Text style={styles.rankDescription}>{rank.description}</Text>
              </View>
            </View>
          ))}
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Popülerliğinizi Nasıl Artırabilirsiniz?</Text>
          <View style={styles.tipItem}>
            <Ionicons name="create" size={24} color="#3498db" style={styles.tipIcon} />
            <Text style={styles.tipText}>Düzenli olarak kaliteli içerik paylaşın</Text>
          </View>
          <View style={styles.tipItem}>
            <Ionicons name="people" size={24} color="#3498db" style={styles.tipIcon} />
            <Text style={styles.tipText}>Diğer kullanıcılarla etkileşimde bulunun</Text>
          </View>
          <View style={styles.tipItem}>
            <Ionicons name="chatbubbles" size={24} color="#3498db" style={styles.tipIcon} />
            <Text style={styles.tipText}>Yorumlara yanıt verin ve tartışmalara katılın</Text>
          </View>
          <View style={styles.tipItem}>
            <Ionicons name="share-social" size={24} color="#3498db" style={styles.tipIcon} />
            <Text style={styles.tipText}>Platformdaki varlığınızı artırın</Text>
          </View>
        </View>

        <View style={styles.footer}>
          <Text style={styles.footerText}>
            © 2025 BekTech. Tüm hakları saklıdır.
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: 40,
    paddingBottom: 10,
    paddingHorizontal: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#333',
  },
  backButton: {
    padding: 5,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
    marginLeft: 15,
  },
  scrollView: {
    flex: 1,
    padding: 15,
  },
  section: {
    marginBottom: 25,
    backgroundColor: '#111',
    borderRadius: 10,
    padding: 15,
    borderWidth: 1,
    borderColor: '#333',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 10,
  },
  sectionText: {
    fontSize: 14,
    color: '#ccc',
    lineHeight: 20,
    marginBottom: 15,
  },
  formulaContainer: {
    marginVertical: 15,
    backgroundColor: '#1a1a1a',
    borderRadius: 8,
    padding: 15,
  },
  formulaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  formulaText: {
    color: '#ddd',
    fontSize: 14,
    marginLeft: 10,
  },
  noteText: {
    fontSize: 12,
    color: '#999',
    fontStyle: 'italic',
    marginTop: 10,
  },
  rankItem: {
    flexDirection: 'row',
    marginBottom: 15,
    backgroundColor: '#1a1a1a',
    borderRadius: 8,
    padding: 10,
    alignItems: 'center',
  },
  rankBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 15,
    marginRight: 10,
  },
  rankBadgeText: {
    color: '#000',
    fontWeight: 'bold',
    fontSize: 14,
  },
  rankInfo: {
    flex: 1,
  },
  rankThreshold: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 14,
    marginBottom: 4,
  },
  rankDescription: {
    color: '#aaa',
    fontSize: 12,
  },
  tipItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    backgroundColor: '#1a1a1a',
    borderRadius: 8,
    padding: 10,
  },
  tipIcon: {
    marginRight: 10,
  },
  tipText: {
    color: '#ddd',
    fontSize: 14,
  },
  footer: {
    marginTop: 10,
    marginBottom: 30,
    alignItems: 'center',
  },
  footerText: {
    color: '#666',
    fontSize: 12,
  },
});

export default PopularityInfoScreen;
