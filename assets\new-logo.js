import React from 'react';
import { View } from 'react-native';
import Svg, { Path, Circle, G, LinearGradient, Stop, Defs } from 'react-native-svg';

const NewLogo = ({ width = 200, height = 200 }) => {
  return (
    <View style={{ width, height }}>
      <Svg width={width} height={height} viewBox="0 0 200 200">
        <Defs>
          <LinearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
            <Stop offset="0%" stopColor="#0066CC" stopOpacity="1" />
            <Stop offset="100%" stopColor="#1DA1F2" stopOpacity="1" />
          </LinearGradient>
        </Defs>
        
        {/* Ana Daire */}
        <Circle cx="100" cy="100" r="90" fill="none" stroke="url(#grad)" strokeWidth="4" />
        
        {/* Müzik Notası */}
        <G transform="translate(70,90) scale(1.2)">
          <Path
            d="M20,0 L20,50 C20,65 0,65 0,50 C0,35 20,35 20,50"
            stroke="url(#grad)"
            strokeWidth="4"
            fill="none"
          />
          <Circle cx="0" cy="50" r="8" fill="url(#grad)" />
        </G>
        
        {/* Şiir Kalemi */}
        <G transform="translate(120,90) rotate(-45)">
          <Path
            d="M0,0 L30,0 L30,60 L15,70 L0,60 Z"
            fill="url(#grad)"
          />
          <Path
            d="M10,65 L20,65"
            stroke="#fff"
            strokeWidth="2"
          />
        </G>
      </Svg>
    </View>
  );
};

export default NewLogo;