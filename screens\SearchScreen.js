import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TextInput,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  ActivityIndicator,
  Image,
  ScrollView,
  Animated
} from 'react-native';
import { auth } from '../firebase';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { listenToLeaderboard } from '../utils/popularityUtils';

// LinearGradient bileşeni için try-catch bloğu
let LinearGradient;
try {
  LinearGradient = require('expo-linear-gradient').LinearGradient;
} catch (error) {
  // LinearGradient yüklü de<PERSON>, basit bir View kullan
  LinearGradient = ({ children, style }) => <View style={style}>{children}</View>;
}

// Avatar mapping
const avatarMap = {
  avatar1: require('../assets/avatar1.png'),
  avatar2: require('../assets/avatar2.png'),
  avatar3: require('../assets/avatar3.png'),
  avatar4: require('../assets/avatar4.png'),
  avatar5: require('../assets/avatar5.png'),
  avatar6: require('../assets/avatar6.png'),
  avatar7: require('../assets/avatar7.png'),
  avatar8: require('../assets/avatar8.png'),
  avatar9: require('../assets/avatar9.png'),
  avatar10: require('../assets/avatar10.png'),
  avatar11: require('../assets/avatar11.png'),
  avatar12: require('../assets/avatar12.png'),
  avatar13: require('../assets/avatar13.png'),
  avatar14: require('../assets/avatar14.png'),
};

const SearchScreen = () => {
  const [searchText, setSearchText] = useState('');
  const [results, setResults] = useState([]);
  const [loading, setLoading] = useState(false);
  const [allUsers, setAllUsers] = useState([]);
  const [allTimePopular, setAllTimePopular] = useState([]);
  const [dailyStars, setDailyStars] = useState([]);
  const [hourlyTrending, setHourlyTrending] = useState([]);
  const [mostFollowed, setMostFollowed] = useState([]);
  const [loadingData, setLoadingData] = useState(true);
  const navigation = useNavigation();

  // Liderlik tablosunu canlı olarak dinle
  useEffect(() => {
    setLoadingData(true);

    // Liderlik tablosunu dinle
    const unsubscribe = listenToLeaderboard((leaderboardData) => {
      // Tüm kullanıcıları güncelle
      const allUsers = [
        ...leaderboardData.allTimePopular,
        ...leaderboardData.dailyStars,
        ...leaderboardData.hourlyTrending,
        ...leaderboardData.mostFollowed
      ];

      // Tekrarlanan kullanıcıları kaldır
      const uniqueUsers = Array.from(new Map(allUsers.map(user => [user.uid, user])).values());
      setAllUsers(uniqueUsers);

      // Liderlik tablosu kategorilerini güncelle
      setAllTimePopular(leaderboardData.allTimePopular);
      setDailyStars(leaderboardData.dailyStars);
      setHourlyTrending(leaderboardData.hourlyTrending);
      setMostFollowed(leaderboardData.mostFollowed);

      setLoadingData(false);
    });

    // Component unmount olduğunda dinleyiciyi temizle
    return () => {
      unsubscribe();
    };
  }, []);

  const handleSearch = async (text) => {
    setSearchText(text);
    if (text.trim().length === 0) {
      setResults([]);
      return;
    }

    setLoading(true);
    try {
      // Büyük/küçük harf duyarsız arama için metni küçük harfe çeviriyoruz
      const searchTextLower = text.toLowerCase();

      // Mevcut kullanıcı listesinden filtreleme yap
      let filteredUsers = allUsers.filter(user => {
        const username = (user.username || '').toLowerCase();
        return username.includes(searchTextLower);
      });

      // Not: Kullanıcı kendisini aratabilir, kendisini aratınca Hesap.js'ye yönlendirilecek
      // Eğer kullanıcının kendisini sonuçlarda görmesini istemiyorsanız, aşağıdaki kodu aktif edin:
      /*
      if (auth.currentUser) {
        filteredUsers = filteredUsers.filter(user => user.uid !== auth.currentUser.uid);
      }
      */

      setResults(filteredUsers);
    } catch (error) {
      console.error("Arama hatası:", error);
    }
    setLoading(false);
  };

  const renderItem = ({ item }) => {
    // Kullanıcının kendisi ise Hesap.js'ye yönlendir
    const isCurrentUser = item.uid === auth.currentUser?.uid;
    const navigateTo = isCurrentUser ? 'Profil' : 'OtherProfile';
    const navigationParams = isCurrentUser ? {} : { uid: item.uid };

    return (
      <TouchableOpacity
        style={styles.itemContainer}
        onPress={() => navigation.navigate(navigateTo, navigationParams)}
      >
        <LinearGradient
          colors={['#1a1a1a', '#222']}
          style={styles.itemGradient}
        >
          <View style={styles.itemContent}>
            <Image
              source={item.avatarIndex !== undefined
                ? avatarMap['avatar' + (item.avatarIndex + 1)]
                : require('../assets/default-avatar.png')}
              style={styles.profileImage}
            />
            <View style={styles.userInfo}>
              <Text style={styles.itemText}>{item.username}</Text>
              <Text style={styles.userStats}>
                {item.followers?.length || 0} takipçi • {item.popularity} popülerlik
              </Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color="#666" />
          </View>
        </LinearGradient>
      </TouchableOpacity>
    );
  };

  // Kullanıcı kartı bileşeni
  const UserCard = ({ user, index, colorScheme }) => {
    const isCurrentUser = user.uid === auth.currentUser?.uid;
    const navigateTo = isCurrentUser ? 'Profil' : 'OtherProfile';
    const navigationParams = isCurrentUser ? {} : { uid: user.uid };

    // Renk şemaları
    const colors = {
      allTime: ['#0066CC', '#004C99'],
      daily: ['#FF9900', '#CC7A00'],
      hourly: ['#CC33FF', '#9900CC'],
      followers: ['#00CC99', '#009973']
    };

    // Madalya renkleri
    const medalColors = {
      0: '#FFD700', // Altın
      1: '#C0C0C0', // Gümüş
      2: '#CD7F32'  // Bronz
    };

    return (
      <TouchableOpacity
        style={[
          styles.userCard,
          index < 3 ? {borderColor: medalColors[index], borderWidth: 2} : {}
        ]}
        onPress={() => navigation.navigate(navigateTo, navigationParams)}
      >
        {index < 3 && (
          <View style={[styles.rankBadge, {backgroundColor: colors[colorScheme][0]}]}>
            <Text style={styles.rankText}>{index + 1}</Text>
          </View>
        )}
        <Image
          source={user.avatarIndex !== undefined
            ? avatarMap['avatar' + (user.avatarIndex + 1)]
            : require('../assets/default-avatar.png')}
          style={[
            styles.userImage,
            index < 3 ? {borderColor: medalColors[index], borderWidth: 3} : {}
          ]}
        />
        <Text style={styles.userName} numberOfLines={1}>{user.username}</Text>

        {colorScheme === 'allTime' && (
          <Text style={[styles.userStat, {color: colors.allTime[0]}]}>
            {user.popularity} popülerlik
          </Text>
        )}

        {colorScheme === 'daily' && (
          <Text style={[styles.userStat, {color: colors.daily[0]}]}>
            {user.dailyPopularity} popülerlik
          </Text>
        )}

        {colorScheme === 'hourly' && (
          <Text style={[styles.userStat, {color: colors.hourly[0]}]}>
            {user.hourlyPopularity} popülerlik
          </Text>
        )}

        {colorScheme === 'followers' && (
          <Text style={[styles.userStat, {color: colors.followers[0]}]}>
            {user.followers?.length || 0} takipçi
          </Text>
        )}
      </TouchableOpacity>
    );
  };

  // Liderlik tablosu bölümü
  const LeaderboardSection = ({ title, users, colorScheme, icon }) => {
    // Canlı kullanıcı verileri için state
    const [liveUsers, setLiveUsers] = useState(users);
    // Animasyon değerleri
    const animValues = useRef({});
    // Son güncelleme zamanı
    const lastUpdateRef = useRef(Date.now());

    // Kullanıcı listesi değiştiğinde, canlı takip başlat
    useEffect(() => {
      // Kullanıcı listesi değiştiğinde, animasyon değerlerini sıfırla
      animValues.current = {};

      // Kullanıcı listesini güncelle
      setLiveUsers(users);

      // Her kullanıcı için animasyon değeri oluştur
      users.forEach(user => {
        if (!user.uid) return;
        animValues.current[user.uid] = new Animated.Value(1);
      });

      // Kullanıcı listesi değiştiğinde animasyon yap
      if (Date.now() - lastUpdateRef.current > 1000) {
        users.forEach(user => {
          if (!user.uid || !animValues.current[user.uid]) return;

          Animated.sequence([
            Animated.timing(animValues.current[user.uid], {
              toValue: 1.1,
              duration: 200,
              useNativeDriver: true
            }),
            Animated.timing(animValues.current[user.uid], {
              toValue: 1,
              duration: 200,
              useNativeDriver: true
            })
          ]).start();
        });

        lastUpdateRef.current = Date.now();
      }
    }, [users]);

    if (loadingData) {
      return (
        <View style={styles.sectionContainer}>
          <View style={styles.sectionHeader}>
            <Ionicons name={icon} size={22} color="#fff" style={styles.sectionIcon} />
            <Text style={styles.sectionTitle}>{title}</Text>
          </View>
          <ActivityIndicator size="large" color="#0066CC" />
        </View>
      );
    }

    // Renk şemaları
    const colors = {
      allTime: ['#0066CC', '#004C99'],
      daily: ['#FF9900', '#CC7A00'],
      hourly: ['#CC33FF', '#9900CC'],
      followers: ['#00CC99', '#009973']
    };

    return (
      <View style={styles.sectionContainer}>
        <LinearGradient
          colors={['#111', '#222']}
          style={styles.sectionGradient}
        >
          <View style={styles.sectionHeader}>
            <Ionicons name={icon} size={22} color="#fff" style={styles.sectionIcon} />
            <Text style={[styles.sectionTitle, {color: colors[colorScheme][0]}]}>{title}</Text>
          </View>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            {liveUsers.map((user, index) => (
              <Animated.View
                key={user.uid}
                style={{
                  transform: [{ scale: animValues.current[user.uid] || 1 }]
                }}
              >
                <UserCard
                  user={user}
                  index={index}
                  colorScheme={colorScheme}
                />
              </Animated.View>
            ))}
          </ScrollView>
        </LinearGradient>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      {/* Üst Çubuk */}
      <View style={styles.topHeader}>
        <Text style={styles.topTitle}>VELMORA</Text>
      </View>

      {/* Arama çubuğu */}
      <View style={styles.searchContainer}>
        <Ionicons name="search" size={20} color="#666" style={styles.searchIcon} />
        <TextInput
          style={styles.input}
          placeholder="Kullanıcı Ara..."
          placeholderTextColor="#666"
          value={searchText}
          onChangeText={handleSearch}
        />
        {searchText.length > 0 && (
          <TouchableOpacity onPress={() => setSearchText('')} style={styles.clearButton}>
            <Ionicons name="close-circle" size={20} color="#666" />
          </TouchableOpacity>
        )}
      </View>

      {/* Arama sonuçları veya liderlik tablosu */}
      {searchText.trim().length === 0 ? (
        <ScrollView style={styles.leaderboardContainer} showsVerticalScrollIndicator={false}>
          <Text style={styles.mainTitle}>Liderlik Tablosu</Text>

          {/* Tüm zamanların en popülerleri */}
          <LeaderboardSection
            title="Tüm Zamanların En Ünlüleri"
            users={allTimePopular}
            colorScheme="allTime"
            icon="trophy"
          />

          {/* Günün yıldızları */}
          <LeaderboardSection
            title="Günün Yıldızları"
            users={dailyStars}
            colorScheme="daily"
            icon="star"
          />

          {/* Saatin trendleri */}
          <LeaderboardSection
            title="Saatin Trendleri"
            users={hourlyTrending}
            colorScheme="hourly"
            icon="trending-up"
          />

          {/* En çok takip edilenler */}
          <LeaderboardSection
            title="En Çok Takip Edilenler"
            users={mostFollowed}
            colorScheme="followers"
            icon="people"
          />

          {/* Alt boşluk */}
          <View style={{height: 20}} />
        </ScrollView>
      ) : (
        <View style={styles.resultsContainer}>
          <View style={styles.resultsHeader}>
            <Ionicons name="list" size={20} color="#fff" />
            <Text style={styles.resultsTitle}>Arama Sonuçları</Text>
          </View>

          {loading ? (
            <ActivityIndicator size="large" color="#fff" style={{ marginTop: 20 }} />
          ) : (
            <FlatList
              data={results}
              keyExtractor={(item) => item.uid}
              renderItem={renderItem}
              style={styles.list}
              ListEmptyComponent={
                <View style={styles.emptyContainer}>
                  <Ionicons name="search-outline" size={50} color="#666" />
                  <Text style={styles.emptyText}>
                    Arama sonucu bulunamadı.
                  </Text>
                </View>
              }
            />
          )}
        </View>
      )}
    </View>
  );
};

export default SearchScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
    padding: 10,
  },
  // Üst Çubuk
  topHeader: {
    width: '100%',
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#333',
    marginBottom: 15,
    backgroundColor: '#000',
  },
  topTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#fff',
  },
  // Arama çubuğu
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#1a1a1a',
    borderRadius: 10,
    paddingHorizontal: 15,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: '#333',
  },
  searchIcon: {
    marginRight: 10,
  },
  input: {
    flex: 1,
    color: '#fff',
    padding: 12,
    fontSize: 16,
  },
  clearButton: {
    padding: 5,
  },
  // Ana başlık
  mainTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 15,
    textAlign: 'center',
    textShadowColor: 'rgba(255, 255, 255, 0.3)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 3,
  },
  // Liderlik tablosu
  leaderboardContainer: {
    flex: 1,
  },
  sectionContainer: {
    marginBottom: 20,
  },
  sectionGradient: {
    borderRadius: 12,
    padding: 15,
    borderWidth: 1,
    borderColor: '#333',
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  sectionIcon: {
    marginRight: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  userCard: {
    alignItems: 'center',
    marginRight: 15,
    width: 100,
    backgroundColor: '#1a1a1a',
    padding: 12,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: '#333',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 3,
    elevation: 3,
  },
  userImage: {
    width: 70,
    height: 70,
    borderRadius: 35,
    borderWidth: 2,
    borderColor: '#0066CC',
    marginBottom: 8,
  },
  userName: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 3,
  },
  userStat: {
    fontSize: 12,
    textAlign: 'center',
    fontWeight: 'bold',
  },
  // Rozet stilleri
  rankBadge: {
    position: 'absolute',
    top: -5,
    right: -5,
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
    borderWidth: 1,
    borderColor: '#fff',
  },
  rankText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
  },
  // Arama sonuçları
  resultsContainer: {
    flex: 1,
  },
  resultsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
    justifyContent: 'center',
  },
  resultsTitle: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  list: {
    flex: 1,
  },
  itemContainer: {
    marginBottom: 10,
    borderRadius: 10,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 3,
    elevation: 3,
  },
  itemGradient: {
    padding: 12,
  },
  itemContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  profileImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
    borderWidth: 2,
    borderColor: '#0066CC',
    marginRight: 12,
  },
  userInfo: {
    flex: 1,
  },
  itemText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 3,
  },
  userStats: {
    color: '#aaa',
    fontSize: 12,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 50,
  },
  emptyText: {
    color: '#666',
    textAlign: 'center',
    marginTop: 10,
    fontSize: 16,
  },
});
