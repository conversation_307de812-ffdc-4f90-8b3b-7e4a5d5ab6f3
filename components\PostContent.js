import React, { useState, useEffect } from 'react';
import { Text, TouchableOpacity, StyleSheet, View } from 'react-native';
import { useLanguage } from '../contexts/LanguageContext';
import { useTheme } from '../contexts/ThemeContext';

const PostContent = ({ content, maxLength = 150, style }) => {
  const { translations } = useLanguage();
  const { theme } = useTheme();
  const [showFullContent, setShowFullContent] = useState(false);
  const [needsReadMore, setNeedsReadMore] = useState(false);

  useEffect(() => {
    // Check if content is longer than maxLength
    setNeedsReadMore(content.length > maxLength);
  }, [content, maxLength]);

  const toggleContent = () => {
    setShowFullContent(!showFullContent);
  };

  const displayContent = showFullContent ? content : content.slice(0, maxLength) + (needsReadMore ? '...' : '');

  return (
    <View>
      <Text style={[styles.content, { color: theme.text }, style]}>
        {displayContent}
      </Text>
      {needsReadMore && (
        <TouchableOpacity onPress={toggleContent} style={styles.readMoreButton}>
          <Text style={[styles.readMoreText, { color: '#3B82F6' }]}>
            {showFullContent ? (translations.readLess || 'Read Less') : (translations.readMore || 'Read More')}
          </Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  content: {
    fontSize: 16,
    lineHeight: 22,
    marginBottom: 5,
  },
  readMoreButton: {
    marginTop: 2,
    marginBottom: 8,
  },
  readMoreText: {
    fontSize: 14,
    fontWeight: '500',
  },
});

export default PostContent;
